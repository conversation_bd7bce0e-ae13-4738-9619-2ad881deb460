package com.sevb.system.service.impl;

import com.alibaba.excel.EasyExcel;
import com.sevb.common.util.RequestReturnVO;
import com.sevb.system.dao.IMenuDao;
import com.sevb.system.entity.vo.MenuBO;
import com.sevb.system.entity.vo.MenuInputVO;
import com.sevb.system.entity.vo.MenuVO;
import com.sevb.system.excel.MenuUploadDataListener1;
import com.sevb.system.service.IMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;

@Service
@Api("菜单服务")
public class MenuService implements IMenuService {
    @Autowired
    private IMenuDao menuDao;

    private Integer maxLevel = 0;

    private Map<String, String> pathMap = new HashMap<>();

    @Override
    @ApiOperation(value = "菜单类-查询服务")
    public RequestReturnVO getMenuData (MenuInputVO menuVO) {
        List<MenuVO> resultList = new ArrayList();
        resultList = menuDao.selectMenuData(menuVO);
        return RequestReturnVO.success(resultList);
    }

    @Override
    @ApiOperation(value = "菜单类-新增服务")
    public RequestReturnVO addMenuData (MenuVO menuVO) {
        int result = 0;
        result = menuDao.insertMenuData(menuVO);
        return RequestReturnVO.success(result);
    }

    @Override
    @ApiOperation(value = "菜单类-修改服务")
    public RequestReturnVO changeMenuData (MenuVO menuVO) {
        int result = 0;
        result = menuDao.updateMenuData(menuVO);
        return RequestReturnVO.success(result);
    }

    @Override
    @ApiOperation(value = "菜单类-删除服务")
    public RequestReturnVO removeMenuData (MenuInputVO menuVO) {
        int result = 0;
        result = menuDao.deleteMenuData(menuVO);
        return RequestReturnVO.success(result);
    }

    @Override
    @ApiOperation(value = "菜单类-批量修改服务")
    public RequestReturnVO changeMenuDataBatch (List<MenuVO> menuVOList) {
        int result = 0;
        result = menuDao.updateMenuDataBatch(menuVOList);
        return RequestReturnVO.success(result);
    }

    @Override
    @ApiOperation(value = "菜单类-批量新增服务")
    public RequestReturnVO addMenuDataBatch (List<MenuVO> menuVOList) {
        int result = 0;
        result = menuDao.insertMenuDataBatch(menuVOList);
        return RequestReturnVO.success(result);
    }

    @Override
    @ApiOperation(value = "菜单类-批量删除服务")
    public RequestReturnVO removeMenuDataBatch (List<MenuInputVO> menuVOList) {
        int result = 0;
        result = menuDao.deleteMenuDataBatch(menuVOList);
        return RequestReturnVO.success(result);
    }

    @Override
    @ApiOperation(value = "菜单类-导出服务")
    public void download (HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        String fileName = URLEncoder.encode("demo", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        EasyExcel.write(response.getOutputStream(), MenuVO.class).sheet("模板").doWrite(data());

//        return RequestReturnVO.success(1);
    }

    private List<MenuVO> data() {
        MenuInputVO menuVO = new MenuInputVO();
        List<MenuVO> resultList = new ArrayList();
        resultList = menuDao.selectMenuData(menuVO);
        return resultList;
    }

    @Override
    @ApiOperation(value = "菜单类-导入服务")
    public void upload (MultipartFile file) throws IOException {
        EasyExcel.read(file.getInputStream(), MenuVO.class, new MenuUploadDataListener1(menuDao)).sheet().doRead();
//        return RequestReturnVO.success("success");
    }

    @Override
    @ApiOperation(value = "菜单类-构建菜单树服务")
    public RequestReturnVO buildMenuTree (MenuInputVO menuVO) {
        List<MenuVO> menuVOList = new ArrayList();
        List<MenuBO> menuBOList = new ArrayList<>();
        List<MenuBO> resultList = new ArrayList<>();
        if (menuVO.getUserCode().equals("admin")) {
            menuVOList = menuDao.selectMenuData(menuVO);
        } else {
            menuVOList = menuDao.selectMenuDataByUser(menuVO);
        }
        pathMap = initPathMap(menuVOList);
        menuBOList = bfsMenuTree(menuVOList);
        resultList = genMenuTree(menuBOList);
        return RequestReturnVO.success(resultList);
    }

    @Override
    @ApiOperation(value = "菜单类-获取指定菜单的子菜单服务")
    public RequestReturnVO getSubMenuTreeByKey (MenuInputVO menuInputVO) {
        List<MenuBO> resultList = new ArrayList<>();
        MenuInputVO menuVO = new MenuInputVO();
        List<MenuBO> menuBOList = (List<MenuBO>)buildMenuTree(menuInputVO).getResultObjVO();
        for (MenuBO menuBO : menuBOList) {
            if (menuBO.getKey().equals(menuInputVO.getMenuPath())) {
                resultList = menuBO.getChildren();
            }
        }
        return RequestReturnVO.success(resultList);
    }

    @Override
    @ApiOperation(value = "菜单类-获取指定层级的菜单服务")
    public RequestReturnVO getSubMenuTreeByLevel (MenuInputVO menuInputVO) {
        List<MenuBO> resultList = new ArrayList<>();
        MenuInputVO menuVO = new MenuInputVO();
        List<MenuBO> menuBOList = (List<MenuBO>)buildMenuTree(menuInputVO).getResultObjVO();
        for (MenuBO menuBO : menuBOList) {
            if (menuBO.getLevel() == menuInputVO.getMenuLevel()) {
                resultList.add(menuBO);
            }
        }
        return RequestReturnVO.success(resultList);
    }

    @Override
    @ApiOperation(value = "菜单类-获取指定Key的菜单路径服务")
    public RequestReturnVO getMenuPathByKey (MenuInputVO menuInputVO) {
        String key = menuInputVO.getMenuPath();
        MenuInputVO myMenuInputVO = new MenuInputVO();
        MenuVO tmpMenuVO = new MenuVO();
        List<MenuBO> resultList = new ArrayList<>();
        List<MenuVO> menuVOList = new ArrayList<>();
        if (menuInputVO.getUserCode().equals("admin")) {
            menuVOList = menuDao.selectMenuData(menuInputVO);
        } else {
            menuVOList = menuDao.selectMenuDataByUser(menuInputVO);
        }
        Map<String, MenuVO> map = new HashMap<>();
        for (MenuVO menuVO : menuVOList) {
            if (key.equals(menuVO.getMenuPath())) {
                tmpMenuVO = menuVO;
            }
            map.put(menuVO.getMenuPath(),menuVO);
        }
        boolean rootFlag = false;
        MenuVO tmpNode = new MenuVO();
        while (!rootFlag && !tmpMenuVO.getMenuPath().equals("root")) {
            MenuBO menuBO = new MenuBO();
            menuBO.setId(tmpMenuVO.getMenuId());
            menuBO.setTitle(tmpMenuVO.getMenuName());
            menuBO.setKey(tmpMenuVO.getMenuPath());
            menuBO.setIcon(tmpMenuVO.getMenuType());
            resultList.add(0,menuBO);
            rootFlag = tmpMenuVO.getParentMenuCode().equals("root") ? true : false;
            tmpNode = map.getOrDefault(tmpMenuVO.getParentMenuCode(),new MenuVO());
            tmpMenuVO = tmpNode;
        }
        return RequestReturnVO.success(resultList);
    }

    private Map<String, String> initPathMap(List<MenuVO> menuVOList) {
        Map<String, String> pathMap = new HashMap<>();
        for (MenuVO menuVO : menuVOList) {
            pathMap.put(menuVO.getMenuPath(), menuVO.getParentMenuCode());
        }
        return pathMap;
    }

    private List<MenuBO> bfsMenuTree(List<MenuVO> menuVOList) {
        List<MenuBO> resultList = new ArrayList<>();
        Queue<MenuVO> queue = new LinkedList<>();
        MenuVO root = new MenuVO();
        root.setMenuPath("root");
        queue.offer(root);
        Integer lvl = 0;

        while (!queue.isEmpty()) {
            int size = queue.size();
            for (int i = 0; i < queue.size(); i++) {
                MenuVO tmpNode = queue.poll();
                MenuBO menuBO = new MenuBO();
                if (!tmpNode.getMenuPath().equals("root")) {
                    menuBO.setId(tmpNode.getMenuId());
                    menuBO.setCode(tmpNode.getMenuCode());
                    menuBO.setTitle(tmpNode.getMenuName());
                    menuBO.setKey(tmpNode.getMenuPath());
                    menuBO.setParentKey(tmpNode.getParentMenuCode());
                    menuBO.setIcon(tmpNode.getMenuType());
                    menuBO.setSortNo(tmpNode.getMenuSortNo());
                    menuBO.setStatus(tmpNode.getStatus());
                    menuBO.setRemark(tmpNode.getRemark());
                    menuBO.setCreatedBy(tmpNode.getCreatedBy());
                    menuBO.setCreationDate(tmpNode.getCreationDate());
                    menuBO.setLastUpdatedBy(tmpNode.getLastUpdatedBy());
                    menuBO.setLastUpdateDate(tmpNode.getLastUpdateDate());
                    menuBO.setLevel(lvl);
                    resultList.add(menuBO);
                    if (i==size-1) {
                        lvl++;
                    }
                } else {
                    menuBO.setId("-1");
                    menuBO.setTitle("根结点");
                    menuBO.setKey("root");
                    menuBO.setLevel(lvl);
                    resultList.add(menuBO);
                    lvl++;
                }
                for (MenuVO menuVO : menuVOList) {
                    if (menuVO.getParentMenuCode().equals(tmpNode.getMenuPath())) {
                        queue.offer(menuVO);
                    }
                }
            }
        }
        maxLevel = lvl;
        return resultList;
    }

    private List<MenuBO> genMenuTree(List<MenuBO> menuBOList) {
        List<MenuBO> resultList = new ArrayList<>();
        Map<String, List<MenuBO>> map = new HashMap<>();
        for (int i = maxLevel; i > 0; i--) {
            for (MenuBO menuBO : menuBOList) {
                if (menuBO.getLevel() == i ) {
                    String parentPath = pathMap.getOrDefault(menuBO.getKey(),"root");
                    List<MenuBO> node = map.getOrDefault(parentPath,new ArrayList<>());
                    node.add(menuBO);
                    map.put(parentPath, node);
                }
            }
            for (MenuBO menuBO : menuBOList) {
//                if (menuBO.getLevel() == i-1 ) {
                    menuBO.setChildren(map.getOrDefault(menuBO.getKey(),new ArrayList<>()));
//                }
            }
        }
        resultList = map.getOrDefault("root",new ArrayList<>());
        return resultList;
    }

}