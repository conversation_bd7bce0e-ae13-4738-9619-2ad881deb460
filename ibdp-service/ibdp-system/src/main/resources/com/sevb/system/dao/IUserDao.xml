<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sevb.system.dao.IUserDao">
    <select id="selectUserData" parameterType="com.sevb.system.entity.vo.UserInputVO" resultType="com.sevb.system.entity.vo.UserVO">
        SELECT
             USER_ID
            ,USER_CODE
            ,USER_NAME
            ,USER_TYPE
            ,USER_PASSWORD
            ,EMAIL
            ,PHONE
            ,REMARK
            ,STATUS
            ,CREATION_DATE
            ,CREATED_BY
            ,LAST_UPDATE_DATE
            ,LAST_UPDATED_BY
        FROM public.T_IBDP_USER
        WHERE 1=1
        <if test="userId != null and userId != ''">
            AND USER_ID = #{userId}
        </if>
        <if test="userCode != null and userCode != ''">
            AND USER_CODE = #{userCode}
        </if>
        <if test="userName != null and userName != ''">
            AND USER_NAME = #{userName}
        </if>
        <if test="userType != null and userType != ''">
            AND USER_TYPE = #{userType}
        </if>
        <if test="status != null and status != ''">
            AND STATUS = #{status}
        </if>
        <if test="beginDateString != null and beginDateString != ''">
            <![CDATA[
            AND CREATION_DATE >= TO_DATE(#{beginDateString},'YYYY-MM-DD HH24:MI:SS')
            ]]>
        </if>
        <if test="endDateString != null and endDateString != ''">
            <![CDATA[
            AND CREATION_DATE < TO_DATE(#{endDateString},'YYYY-MM-DD HH24:MI:SS')
            ]]>
        </if>
    </select>
    <update id="updateUserData" parameterType="com.sevb.system.entity.vo.UserVO" >
        update public.T_IBDP_USER
        set LAST_UPDATE_DATE = CURRENT_TIMESTAMP
        <if test="userCode != null and userCode != ''">
            ,USER_CODE = #{userCode,jdbcType=VARCHAR}
        </if>
        <if test="userName != null and userName != ''">
            ,USER_NAME = #{userName,jdbcType=VARCHAR}
        </if>
        <if test="userPassword != null and userPassword != ''">
            ,USER_PASSWORD = #{userPassword,jdbcType=VARCHAR}
        </if>
        <if test="userType != null and userType != ''">
            ,USER_TYPE = #{userType,jdbcType=VARCHAR}
        </if>
        <if test="email != null and email != ''">
            ,EMAIL = #{email,jdbcType=VARCHAR}
        </if>
        <if test="phone != null and phone != ''">
            ,PHONE = #{phone,jdbcType=VARCHAR}
        </if>
        <if test="remark != null and remark != ''">
            ,REMARK = #{remark,jdbcType=VARCHAR}
        </if>
        <if test="status != null and status != ''">
            ,STATUS = #{status,jdbcType=VARCHAR}
        </if>
<!--        <if test="createdBy != null and createdBy != ''">-->
<!--            ,CREATED_BY = #{createdBy,jdbcType=VARCHAR}-->
<!--        </if>-->
<!--        <if test="creationDate != null and creationDate != ''">-->
<!--            ,CREATION_DATE = #{creationDate,jdbcType=VARCHAR}-->
<!--        </if>-->
        <if test="lastUpdatedBy != null and lastUpdatedBy != ''">
            ,LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR}
        </if>
        where 1=1
        AND USER_ID = #{userId,jdbcType=VARCHAR}
    </update>

    <insert id = "insertUserData" parameterType="com.sevb.system.entity.vo.UserVO" >
        <selectKey resultType="String" keyProperty="userId" order="BEFORE">
            SELECT
            concat('user' ,TO_CHAR(CURRENT_TIMESTAMP,'YYYYMMDD'), CAST(NEXTVAL('PUBLIC.SEQ_DEMO_ID') as VARCHAR) AS userId
            
        </selectKey>
        INSERT INTO public.T_IBDP_USER (
            USER_ID
            ,USER_CODE
            ,USER_NAME
            ,USER_TYPE
            ,USER_PASSWORD
            ,EMAIL
            ,PHONE
            ,REMARK
            ,STATUS
            ,CREATION_DATE
            ,CREATED_BY
            ,LAST_UPDATE_DATE
            ,LAST_UPDATED_BY
        )
        SELECT
        USER_ID
        ,USER_CODE
        ,USER_NAME
        ,USER_TYPE
        ,USER_PASSWORD
        ,EMAIL
        ,PHONE
        ,REMARK
        ,STATUS
        ,CREATION_DATE
        ,CREATED_BY
        ,LAST_UPDATE_DATE
        ,LAST_UPDATED_BY
        FROM (
           SELECT
         #{userId,jdbcType=VARCHAR} as USER_ID
        ,#{userCode,jdbcType=VARCHAR} AS USER_CODE
        ,#{userName,jdbcType=VARCHAR} AS USER_NAME
        ,#{userType,jdbcType=VARCHAR} AS USER_TYPE
        ,#{userPassword,jdbcType=VARCHAR} AS USER_PASSWORD
        ,#{email,jdbcType=VARCHAR} AS EMAIL
        ,#{phone,jdbcType=VARCHAR} AS PHONE
        ,#{remark,jdbcType=VARCHAR} AS REMARK
        ,#{status,jdbcType=INTEGER} AS STATUS
        ,#{createdBy,jdbcType=VARCHAR} AS CREATED_BY
        ,CURRENT_TIMESTAMP AS CREATION_DATE
        ,#{lastUpdatedBy,jdbcType=VARCHAR} AS LAST_UPDATED_BY
        ,CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
           
        ) T
        WHERE 1=1
    </insert>

    <delete id="deleteUserData" parameterType="com.sevb.system.entity.vo.UserInputVO">
        DELETE FROM public.T_IBDP_USER
        WHERE 1=1
        AND USER_ID = #{userId,jdbcType=VARCHAR}
    </delete>

    <insert id = "insertUserDataBatch" parameterType="com.sevb.system.entity.vo.UserVO" >
        INSERT INTO public.T_IBDP_USER (
        USER_ID
        ,USER_CODE
        ,USER_NAME
        ,USER_TYPE
        ,USER_PASSWORD
        ,EMAIL
        ,PHONE
        ,REMARK
        ,STATUS
        ,CREATION_DATE
        ,CREATED_BY
        ,LAST_UPDATE_DATE
        ,LAST_UPDATED_BY
        )
        SELECT
        concat('user' ,TO_CHAR(CURRENT_TIMESTAMP,'YYYYMMDD'), CAST(NEXTVAL('PUBLIC.SEQ_DEMO_ID') as VARCHAR) AS USER_ID
        ,USER_CODE
        ,USER_NAME
        ,USER_TYPE
        ,USER_PASSWORD
        ,EMAIL
        ,PHONE
        ,REMARK
        ,STATUS
        ,CREATION_DATE
        ,CREATED_BY
        ,LAST_UPDATE_DATE
        ,LAST_UPDATED_BY
        FROM (
        <foreach collection="records" item="item" separator="UNION ALL" >
        SELECT
         #{item.userCode,jdbcType=VARCHAR} AS USER_CODE
        ,#{item.userName,jdbcType=VARCHAR} AS USER_NAME
        ,#{item.userPassword,jdbcType=VARCHAR} AS USER_PASSWORD
        ,#{item.userType,jdbcType=VARCHAR} AS USER_TYPE
        ,#{item.email,jdbcType=VARCHAR} AS EMAIL
        ,#{item.phone,jdbcType=VARCHAR} AS PHONE
        ,#{item.remark,jdbcType=VARCHAR} AS REMARK
        ,#{item.status,jdbcType=VARCHAR} AS STATUS
        ,#{item.createdBy,jdbcType=VARCHAR} AS CREATED_BY
        ,CURRENT_TIMESTAMP AS CREATION_DATE
        ,#{item.lastUpdatedBy,jdbcType=VARCHAR} AS LAST_UPDATED_BY
        ,CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
        
        </foreach>
        ) T
        WHERE 1=1
    </insert>

    <update id="updateUserDataBatch" parameterType="list" >
        <!-- MERGE INTO public.T_IBDP_USER TA
        USING (
        <foreach collection="records" item="item" separator="UNION ALL" >
            SELECT
            #{item.userId,jdbcType=VARCHAR} AS USER_ID
            ,#{item.userCode,jdbcType=VARCHAR} AS USER_CODE
            ,#{item.userName,jdbcType=VARCHAR} AS USER_NAME
            ,#{item.userType,jdbcType=VARCHAR} AS USER_TYPE
            ,#{item.userPassword,jdbcType=VARCHAR} AS USER_PASSWORD
            ,#{item.email,jdbcType=VARCHAR} AS EMAIL
            ,#{item.phone,jdbcType=VARCHAR} AS PHONE
            ,#{item.remark,jdbcType=VARCHAR} AS REMARK
            ,#{item.status,jdbcType=VARCHAR} AS STATUS
            ,#{item.createdBy,jdbcType=VARCHAR} AS CREATED_BY
            ,CURRENT_TIMESTAMP AS CREATION_DATE
            ,#{item.lastUpdatedBy,jdbcType=VARCHAR} AS LAST_UPDATED_BY
            ,CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
            
        </foreach>
        ) TB ON (TA.USER_ID = TB.USER_ID)
        WHEN MATCHED THEN
        UPDATE SET
         TA.USER_CODE = TB.USER_CODE
        ,TA.USER_NAME = TB.USER_NAME
        ,TA.USER_PASSWORD = TB.USER_PASSWORD
        ,TA.USER_TYPE = TB.USER_TYPE
        ,TA.EMAIL = TB.EMAIL
        ,TA.PHONE = TB.PHONE
        ,TA.REMARK = TB.REMARK
        ,TA.STATUS = TB.STATUS
        ,TA.LAST_UPDATED_BY = TB.LAST_UPDATED_BY
        ,TA.LAST_UPDATE_DATE = CURRENT_TIMESTAMP -->
    </update>

    <delete id="deleteUserDataBatch" parameterType="list">
        DELETE FROM public.T_IBDP_USER
        WHERE 1=1
        AND USER_ID IN
        <foreach collection="records" item="item" open="(" separator="," close=")">
            (#{item.userId})
        </foreach>
    </delete>

    <select id="selectRoleIdsByUser" parameterType="com.sevb.system.entity.vo.UserInputVO" resultType="string">
        SELECT
<!--            TC.ROLE_ID-->
            TC.ROLE_CODE
        FROM public.T_IBDP_USER TA
        INNER JOIN public.T_IBDP_RELATION_ROLE_USER TB ON (TA.USER_ID = TB.USER_ID)
        INNER JOIN public.T_IBDP_ROLE TC ON (TB.ROLE_ID = TC.ROLE_ID)
        WHERE 1=1
        <if test="userId != null and userId != ''">
            AND TA.USER_ID = #{userId}
        </if>
        <if test="userCode != null and userCode != ''">
            AND TA.USER_CODE = #{userCode}
        </if>
        <if test="userName != null and userName != ''">
            AND TA.USER_NAME = #{userName}
        </if>
        <if test="status != null and status != ''">
            AND TA.STATUS = #{status}
        </if>
    </select>

    <select id="selectPermissionIdsByUser" parameterType="com.sevb.system.entity.vo.UserInputVO" resultType="string">
        SELECT
<!--            TE.PERMISSION_ID-->
            TE.PERMISSION_CODE
        FROM public.T_IBDP_USER TA
        INNER JOIN public.T_IBDP_RELATION_ROLE_USER TB ON (TA.USER_ID = TB.USER_ID)
        INNER JOIN public.T_IBDP_ROLE TC ON (TB.ROLE_ID = TC.ROLE_ID)
        INNER JOIN public.T_IBDP_RELATION_ROLE_PERMISSION TD ON (TC.ROLE_ID = TD.ROLE_ID)
        INNER JOIN public.T_IBDP_PERMISSION TE ON (TD.PERMISSION_ID = TE.PERMISSION_ID)
        WHERE 1=1
        <if test="userId != null and userId != ''">
            AND TA.USER_ID = #{userId}
        </if>
        <if test="userCode != null and userCode != ''">
            AND TA.USER_CODE = #{userCode}
        </if>
        <if test="userName != null and userName != ''">
            AND TA.USER_NAME = #{userName}
        </if>
        <if test="status != null and status != ''">
            AND TA.STATUS = #{status}
        </if>
    </select>

</mapper>