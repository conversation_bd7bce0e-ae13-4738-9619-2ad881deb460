<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sevb.system.dao.IClassDao">
    <select id="selectClassData" parameterType="com.sevb.system.entity.vo.ClassInputVO" resultType="com.sevb.system.entity.vo.ClassVO">
        SELECT
            CLASS_ID
            ,CLASS_TYPE_ID
            ,CLASS_TYPE_CODE
            ,CLASS_CODE
            ,CLASS_NAME
            ,REMARK
            ,SEGMENT_01
            ,SEGMENT_02
            ,SEGMENT_03
            ,SEGMENT_04
            ,SEGMENT_05
            ,STATUS
            ,CREATED_BY
            ,CREATION_DATE
            ,LAST_UPDATED_BY
            ,LAST_UPDATE_DATE
        FROM public.T_IBDP_CLASS
        WHERE 1=1
        <if test="classTypeId != null and classTypeId != ''">
            AND CLASS_TYPE_ID = #{classTypeId}
        </if>
        <if test="classTypeCode != null and classTypeCode != ''">
            AND CLASS_TYPE_CODE = #{classTypeCode}
        </if>
        <if test="classId != null and classId != ''">
            AND CLASS_ID = #{classId}
        </if>
        <if test="className != null and className != ''">
            AND CLASS_NAME = #{className}
        </if>
        <if test="status != null and status != ''">
            AND STATUS = #{status}
        </if>
        <if test="beginDateString != null and beginDateString != ''">
            <![CDATA[
            AND CREATION_DATE >= TO_DATE(#{beginDateString},'YYYY-MM-DD HH24:MI:SS')
            ]]>
        </if>
        <if test="endDateString != null and endDateString != ''">
            <![CDATA[
            AND CREATION_DATE < TO_DATE(#{endDateString},'YYYY-MM-DD HH24:MI:SS')
            ]]>
        </if>
    </select>
    <update id="updateClassData" parameterType="com.sevb.system.entity.vo.ClassVO" >
        update public.T_IBDP_CLASS
        set LAST_UPDATE_DATE = CURRENT_TIMESTAMP
        <if test="className != null and className != ''">
            ,CLASS_NAME = #{className,jdbcType=VARCHAR}
        </if>
        <if test="segment01 != null and segment01 != ''">
            ,SEGMENT_01 = #{segment01,jdbcType=VARCHAR}
        </if>
        <if test="segment02 != null and segment02 != ''">
            ,SEGMENT_02 = #{segment02,jdbcType=VARCHAR}
        </if>
        <if test="segment03 != null and segment03 != ''">
            ,SEGMENT_03 = #{segment03,jdbcType=VARCHAR}
        </if>
        <if test="segment04 != null and segment04 != ''">
            ,SEGMENT_04 = #{segment04,jdbcType=VARCHAR}
        </if>
        <if test="segment05 != null and segment05 != ''">
            ,SEGMENT_05 = #{segment05,jdbcType=VARCHAR}
        </if>
        <if test="remark != null and remark != ''">
            ,REMARK = #{remark,jdbcType=VARCHAR}
        </if>
        <if test="status != null and status != ''">
            ,STATUS = #{status,jdbcType=VARCHAR}
        </if>
<!--        <if test="createdBy != null and createdBy != ''">-->
<!--            ,CREATED_BY = #{createdBy,jdbcType=VARCHAR}-->
<!--        </if>-->
<!--        <if test="creationDate != null and creationDate != ''">-->
<!--            ,CREATION_DATE = #{creationDate,jdbcType=VARCHAR}-->
<!--        </if>-->
        <if test="lastUpdatedBy != null and lastUpdatedBy != ''">
            ,LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR}
        </if>
        where 1=1
        AND CLASS_ID = #{classId,jdbcType=VARCHAR}
    </update>

    <insert id = "insertClassData" parameterType="com.sevb.system.entity.vo.ClassVO" >
        <selectKey resultType="String" keyProperty="classId" order="BEFORE">
            SELECT
            CONCAT('class', TO_CHAR(CURRENT_TIMESTAMP,'YYYYMMDD'), CAST(NEXTVAL('PUBLIC.SEQ_DEMO_ID') as VARCHAR) AS classId
            
        </selectKey>
        INSERT INTO public.T_IBDP_CLASS (
            CLASS_ID
            ,CLASS_TYPE_ID
            ,CLASS_TYPE_CODE
            ,CLASS_CODE
            ,CLASS_NAME
            ,REMARK
            ,SEGMENT_01
            ,SEGMENT_02
            ,SEGMENT_03
            ,SEGMENT_04
            ,SEGMENT_05
            ,STATUS
            ,CREATED_BY
            ,CREATION_DATE
            ,LAST_UPDATED_BY
            ,LAST_UPDATE_DATE
        )
        SELECT
            CLASS_ID
            ,CLASS_TYPE_ID
            ,CLASS_TYPE_CODE
            ,CLASS_CODE
            ,CLASS_NAME
            ,REMARK
            ,SEGMENT_01
            ,SEGMENT_02
            ,SEGMENT_03
            ,SEGMENT_04
            ,SEGMENT_05
            ,STATUS
            ,CREATED_BY
            ,CREATION_DATE
            ,LAST_UPDATED_BY
            ,LAST_UPDATE_DATE
        FROM (
           SELECT
         #{classId,jdbcType=VARCHAR} AS CLASS_ID
        ,#{classTypeId,jdbcType=VARCHAR} AS CLASS_TYPE_ID
        ,#{classTypeCode,jdbcType=VARCHAR} AS CLASS_TYPE_CODE
        ,#{classCode,jdbcType=VARCHAR} AS CLASS_CODE
        ,#{className,jdbcType=VARCHAR} AS CLASS_NAME
        ,#{remark,jdbcType=VARCHAR} AS REMARK
        ,#{segment01,jdbcType=VARCHAR} AS SEGMENT_01
        ,#{segment02,jdbcType=VARCHAR} AS SEGMENT_02
        ,#{segment03,jdbcType=VARCHAR} AS SEGMENT_03
        ,#{segment04,jdbcType=VARCHAR} AS SEGMENT_04
        ,#{segment05,jdbcType=VARCHAR} AS SEGMENT_05
        ,#{status,jdbcType=INTEGER} AS STATUS
        ,#{createdBy,jdbcType=VARCHAR} AS CREATED_BY
        ,CURRENT_TIMESTAMP AS CREATION_DATE
        ,#{lastUpdatedBy,jdbcType=VARCHAR} AS LAST_UPDATED_BY
        ,CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
           
        ) T
        WHERE 1=1
    </insert>

    <delete id="deleteClassData" parameterType="com.sevb.system.entity.vo.ClassInputVO">
        DELETE FROM public.T_IBDP_CLASS
        WHERE 1=1
        AND CLASS_ID = #{classId,jdbcType=VARCHAR}
    </delete>

    <insert id = "insertClassDataBatch" parameterType="com.sevb.system.entity.vo.ClassVO" >
        INSERT INTO public.T_IBDP_CLASS (
        CLASS_ID
        ,CLASS_TYPE_ID
        ,CLASS_TYPE_CODE
        ,CLASS_CODE
        ,CLASS_NAME
        ,REMARK
        ,SEGMENT_01
        ,SEGMENT_02
        ,SEGMENT_03
        ,SEGMENT_04
        ,SEGMENT_05
        ,STATUS
        ,CREATED_BY
        ,CREATION_DATE
        ,LAST_UPDATED_BY
        ,LAST_UPDATE_DATE
        )
        SELECT
        concat('class' ,TO_CHAR(CURRENT_TIMESTAMP,'YYYYMMDD'), CAST(NEXTVAL('PUBLIC.SEQ_DEMO_ID') as VARCHAR) AS CLASS_ID
        ,CLASS_TYPE_ID
        ,CLASS_TYPE_CODE
        ,CLASS_CODE
        ,CLASS_NAME
        ,REMARK
        ,SEGMENT_01
        ,SEGMENT_02
        ,SEGMENT_03
        ,SEGMENT_04
        ,SEGMENT_05
        ,STATUS
        ,CREATED_BY
        ,CREATION_DATE
        ,LAST_UPDATED_BY
        ,LAST_UPDATE_DATE

        FROM (
        <foreach collection="records" item="item" separator="UNION ALL" >
        SELECT
             #{item.classId,jdbcType=VARCHAR} AS CLASS_ID
            ,#{item.classTypeId,jdbcType=VARCHAR} AS CLASS_TYPE_ID
            ,#{item.classTypeCode,jdbcType=VARCHAR} AS CLASS_TYPE_CODE
            ,#{item.classCode,jdbcType=VARCHAR} AS CLASS_CODE
            ,#{item.className,jdbcType=VARCHAR} AS CLASS_NAME
            ,#{item.remark,jdbcType=VARCHAR} AS REMARK
            ,#{item.segment01,jdbcType=VARCHAR} AS SEGMENT_01
            ,#{item.segment02,jdbcType=VARCHAR} AS SEGMENT_02
            ,#{item.segment03,jdbcType=VARCHAR} AS SEGMENT_03
            ,#{item.segment04,jdbcType=VARCHAR} AS SEGMENT_04
            ,#{item.segment05,jdbcType=VARCHAR} AS SEGMENT_05
            ,#{item.status,jdbcType=VARCHAR} AS STATUS
            ,#{item.createdBy,jdbcType=VARCHAR} AS CREATED_BY
        ,CURRENT_TIMESTAMP AS CREATION_DATE
        ,#{item.lastUpdatedBy,jdbcType=VARCHAR} AS LAST_UPDATED_BY
        ,CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
        
        </foreach>
        ) T
        WHERE 1=1
    </insert>

    <update id="updateClassDataBatch" parameterType="list" >
        <!-- MERGE INTO public.T_IBDP_CLASS TA
        USING (
        <foreach collection="records" item="item" separator="UNION ALL" >
            SELECT
             #{item.classId,jdbcType=VARCHAR} AS CLASS_ID
            ,#{item.classTypeId,jdbcType=VARCHAR} AS CLASS_TYPE_ID
            ,#{item.classTypeCode,jdbcType=VARCHAR} AS CLASS_TYPE_CODE
            ,#{item.classCode,jdbcType=VARCHAR} AS CLASS_CODE
            ,#{item.className,jdbcType=VARCHAR} AS CLASS_NAME
            ,#{item.remark,jdbcType=VARCHAR} AS REMARK
            ,#{item.segment01,jdbcType=VARCHAR} AS SEGMENT_01
            ,#{item.segment02,jdbcType=VARCHAR} AS SEGMENT_02
            ,#{item.segment03,jdbcType=VARCHAR} AS SEGMENT_03
            ,#{item.segment04,jdbcType=VARCHAR} AS SEGMENT_04
            ,#{item.segment05,jdbcType=VARCHAR} AS SEGMENT_05
            ,#{item.status,jdbcType=VARCHAR} AS STATUS
            ,#{item.createdBy,jdbcType=VARCHAR} AS CREATED_BY
            ,CURRENT_TIMESTAMP AS CREATION_DATE
            ,#{item.lastUpdatedBy,jdbcType=VARCHAR} AS LAST_UPDATED_BY
            ,CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
            
        </foreach>
        ) TB ON (TA.CLASS_ID = TB.CLASS_ID)
        WHEN MATCHED THEN
        UPDATE SET
         TA.CLASS_CODE = TB.CLASS_CODE
        ,TA.CLASS_NAME = TB.CLASS_NAME
        ,TA.CLASS_TYPE_CODE = TB.CLASS_TYPE_CODE
        ,TA.SEGMENT_01 = TB.SEGMENT_01
        ,TA.SEGMENT_02 = TB.SEGMENT_02
        ,TA.SEGMENT_03 = TB.SEGMENT_03
        ,TA.SEGMENT_04 = TB.SEGMENT_04
        ,TA.SEGMENT_05 = TB.SEGMENT_05
        ,TA.REMARK = TB.REMARK
        ,TA.STATUS = TB.STATUS
        ,TA.LAST_UPDATED_BY = TB.LAST_UPDATED_BY
        ,TA.LAST_UPDATE_DATE = CURRENT_TIMESTAMP -->
    </update>

    <delete id="deleteClassDataBatch" parameterType="list">
        DELETE FROM public.T_IBDP_CLASS
        WHERE 1=1
        AND CLASS_ID IN
        <foreach collection="records" item="item" open="(" separator="," close=")">
            (#{item.classId})
        </foreach>
    </delete>

</mapper>