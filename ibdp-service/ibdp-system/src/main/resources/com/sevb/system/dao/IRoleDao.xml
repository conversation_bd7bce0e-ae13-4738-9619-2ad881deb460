<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sevb.system.dao.IRoleDao">
    <select id="selectRoleData" parameterType="com.sevb.system.entity.vo.RoleInputVO" resultType="com.sevb.system.entity.vo.RoleVO">
        SELECT
         ROLE_ID
        ,ROLE_CODE
        ,ROLE_NAME
        ,ROLE_TYPE
        ,REMARK
        ,STATUS
        ,CREATION_DATE
        ,CREATED_BY
        ,LAST_UPDATE_DATE
        ,LAST_UPDATED_BY
        FROM public.T_IBDP_ROLE
        WHERE 1=1
        <if test="roleId != null and roleId != ''">
            AND ROLE_ID = #{roleId}
        </if>
        <if test="roleName != null and roleName != ''">
            AND ROLE_NAME = #{roleName}
        </if>
        <if test="roleType != null and roleType != ''">
            AND ROLE_TYPE = #{roleType}
        </if>
        <if test="status != null and status != ''">
            AND STATUS = #{status}
        </if>
        <if test="beginDateString != null and beginDateString != ''">
            <![CDATA[
            AND CREATION_DATE >= TO_DATE(#{beginDateString},'YYYY-MM-DD HH24:MI:SS')
            ]]>
        </if>
        <if test="endDateString != null and endDateString != ''">
            <![CDATA[
            AND CREATION_DATE < TO_DATE(#{endDateString},'YYYY-MM-DD HH24:MI:SS')
            ]]>
        </if>
    </select>
    <update id="updateRoleData" parameterType="com.sevb.system.entity.vo.RoleVO" >
        update public.T_IBDP_ROLE
        set LAST_UPDATE_DATE = CURRENT_TIMESTAMP
        <if test="roleName != null and roleName != ''">
            ,ROLE_NAME = #{roleName,jdbcType=VARCHAR}
        </if>
        <if test="roleType != null and roleType != ''">
            ,ROLE_TYPE = #{roleType,jdbcType=VARCHAR}
        </if>
        <if test="remark != null and remark != ''">
            ,REMARK = #{remark,jdbcType=VARCHAR}
        </if>
        <if test="status != null and status != ''">
            ,STATUS = #{status,jdbcType=VARCHAR}
        </if>
<!--        <if test="createdBy != null and createdBy != ''">-->
<!--            ,CREATED_BY = #{createdBy,jdbcType=VARCHAR}-->
<!--        </if>-->
<!--        <if test="creationDate != null and creationDate != ''">-->
<!--            ,CREATION_DATE = #{creationDate,jdbcType=VARCHAR}-->
<!--        </if>-->
        <if test="lastUpdatedBy != null and lastUpdatedBy != ''">
            ,LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR}
        </if>
        where 1=1
        AND ROLE_ID = #{roleId,jdbcType=VARCHAR}
    </update>

    <insert id = "insertRoleData" parameterType="com.sevb.system.entity.vo.RoleVO" >
        <selectKey resultType="String" keyProperty="roleId" order="BEFORE">
            SELECT
            concat('role' ,TO_CHAR(CURRENT_TIMESTAMP,'YYYYMMDD'), CAST(NEXTVAL('PUBLIC.SEQ_DEMO_ID') as VARCHAR) AS roleId
            
        </selectKey>
        INSERT INTO public.T_IBDP_ROLE (
         ROLE_ID
        ,ROLE_CODE
        ,ROLE_NAME
        ,ROLE_TYPE
        ,REMARK
        ,STATUS
        ,CREATION_DATE
        ,CREATED_BY
        ,LAST_UPDATE_DATE
        ,LAST_UPDATED_BY
        )
        SELECT
         ROLE_ID
        ,ROLE_CODE
        ,ROLE_NAME
        ,ROLE_TYPE
        ,REMARK
        ,STATUS
        ,CREATION_DATE
        ,CREATED_BY
        ,LAST_UPDATE_DATE
        ,LAST_UPDATED_BY
        FROM (
           SELECT
         #{roleId,jdbcType=VARCHAR} AS ROLE_ID
        ,#{roleCode,jdbcType=VARCHAR} AS ROLE_CODE
        ,#{roleName,jdbcType=VARCHAR} AS ROLE_NAME
        ,#{roleType,jdbcType=VARCHAR} AS ROLE_TYPE
        ,#{remark,jdbcType=VARCHAR} AS REMARK
        ,#{status,jdbcType=VARCHAR} AS STATUS
        ,#{createdBy,jdbcType=VARCHAR} AS CREATED_BY
        ,CURRENT_TIMESTAMP AS CREATION_DATE
        ,#{lastUpdatedBy,jdbcType=VARCHAR} AS LAST_UPDATED_BY
        ,CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
           
        ) T
        WHERE 1=1
    </insert>

    <delete id="deleteRoleData" parameterType="com.sevb.system.entity.vo.RoleInputVO">
        DELETE FROM public.T_IBDP_ROLE
        WHERE 1=1
        AND ROLE_ID = #{roleId,jdbcType=VARCHAR}
    </delete>

    <insert id = "insertRoleDataBatch" parameterType="com.sevb.system.entity.vo.RoleVO" >
        INSERT INTO public.T_IBDP_ROLE (
         ROLE_ID
        ,ROLE_CODE
        ,ROLE_NAME
        ,ROLE_TYPE
        ,REMARK
        ,STATUS
        ,CREATION_DATE
        ,CREATED_BY
        ,LAST_UPDATE_DATE
        ,LAST_UPDATED_BY
        )
        SELECT
        concat('role' ,TO_CHAR(CURRENT_TIMESTAMP,'YYYYMMDD'), CAST(NEXTVAL('PUBLIC.SEQ_DEMO_ID') as VARCHAR) AS ROLE_ID
        ,ROLE_CODE
        ,ROLE_NAME
        ,ROLE_TYPE
        ,REMARK
        ,STATUS
        ,CREATION_DATE
        ,CREATED_BY
        ,LAST_UPDATE_DATE
        ,LAST_UPDATED_BY
        FROM (
        <foreach collection="records" item="item" separator="UNION ALL" >
        SELECT
         #{item.roleCode,jdbcType=VARCHAR} AS ROLE_CODE
        ,#{item.roleName,jdbcType=VARCHAR} AS ROLE_NAME
        ,#{item.roleType,jdbcType=VARCHAR} AS ROLE_TYPE
        ,#{item.remark,jdbcType=VARCHAR} AS REMARK
        ,#{item.status,jdbcType=VARCHAR} AS STATUS
        ,#{item.createdBy,jdbcType=VARCHAR} AS CREATED_BY
        ,CURRENT_TIMESTAMP AS CREATION_DATE
        ,#{item.lastUpdatedBy,jdbcType=VARCHAR} AS LAST_UPDATED_BY
        ,CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
        
        </foreach>
        ) T
        WHERE 1=1
    </insert>

    <update id="updateRoleDataBatch" parameterType="list" >
        <!-- MERGE INTO public.T_IBDP_ROLE TA
        USING (
        <foreach collection="records" item="item" separator="UNION ALL" >
            SELECT
             #{item.roleId,jdbcType=VARCHAR} AS ROLE_ID
            ,#{item.roleCode,jdbcType=VARCHAR} AS ROLE_CODE
            ,#{item.roleName,jdbcType=VARCHAR} AS ROLE_NAME
            ,#{item.roleType,jdbcType=VARCHAR} AS ROLE_TYPE
            ,#{item.remark,jdbcType=VARCHAR} AS REMARK
            ,#{item.status,jdbcType=VARCHAR} AS STATUS
            ,#{item.createdBy,jdbcType=VARCHAR} AS CREATED_BY
            ,CURRENT_TIMESTAMP AS CREATION_DATE
            ,#{item.lastUpdatedBy,jdbcType=VARCHAR} AS LAST_UPDATED_BY
            ,CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
            
        </foreach>
        ) TB ON (TA.ROLE_ID = TB.ROLE_ID)
        WHEN MATCHED THEN
        UPDATE SET
         TA.ROLE_CODE = TB.ROLE_CODE
        ,TA.ROLE_NAME = TB.ROLE_NAME
        ,TA.ROLE_TYPE = TB.ROLE_TYPE
        ,TA.REMARK = TB.REMARK
        ,TA.STATUS = TB.STATUS
        ,TA.LAST_UPDATED_BY = TB.LAST_UPDATED_BY
        ,TA.LAST_UPDATE_DATE = CURRENT_TIMESTAMP -->
    </update>

    <delete id="deleteRoleDataBatch" parameterType="list">
        DELETE FROM public.T_IBDP_ROLE
        WHERE 1=1
        AND ROLE_ID IN
        <foreach collection="records" item="item" open="(" separator="," close=")">
            (#{item.roleId})
        </foreach>
    </delete>

    <select id="selectRelationRoleUserData" parameterType="com.sevb.system.entity.vo.RelationVO" resultType="com.sevb.system.entity.vo.RelationVO">
        SELECT
        ID
        ,ROLE_ID
        ,USER_ID
        ,REMARK
        ,STATUS
        ,CREATION_DATE
        ,CREATED_BY
        ,LAST_UPDATE_DATE
        ,LAST_UPDATED_BY
        FROM public.T_IBDP_RELATION_ROLE_USER
        WHERE 1=1
        <if test="roleId != null and roleId != ''">
            AND ROLE_ID = #{roleId}
        </if>
        <if test="userId != null and userId != ''">
            AND USER_ID = #{userId}
        </if>
        <if test="status != null and status != ''">
            AND STATUS = #{status}
        </if>
    </select>

    <delete id="deleteRelationRoleUserData" parameterType="com.sevb.system.entity.vo.RelationVO">
        DELETE FROM public.T_IBDP_RELATION_ROLE_USER
        WHERE 1=1
        <if test="roleId != null and roleId != ''">
            AND ROLE_ID = #{roleId}
        </if>
    </delete>


    <insert id = "insertRelationRoleUserData" parameterType="com.sevb.system.entity.vo.RelationVO" >
        INSERT INTO public.T_IBDP_RELATION_ROLE_USER (
        ID
        ,ROLE_ID
        ,USER_ID
        ,REMARK
        ,STATUS
        ,CREATION_DATE
        ,CREATED_BY
        ,LAST_UPDATE_DATE
        ,LAST_UPDATED_BY
        )
        SELECT
        concat('relation' ,TO_CHAR(CURRENT_TIMESTAMP,'YYYYMMDD'), CAST(NEXTVAL('PUBLIC.SEQ_DEMO_ID') as VARCHAR) AS ID
        ,ROLE_ID
        ,USER_ID
        ,REMARK
        ,STATUS
        ,CREATION_DATE
        ,CREATED_BY
        ,LAST_UPDATE_DATE
        ,LAST_UPDATED_BY
        FROM (
        <foreach collection="records" item="item" separator="UNION ALL" >
            SELECT
            #{item.roleId,jdbcType=VARCHAR} AS ROLE_ID
            ,#{item.userId,jdbcType=VARCHAR} AS USER_ID
            ,#{item.remark,jdbcType=VARCHAR} AS REMARK
            ,#{item.status,jdbcType=VARCHAR} AS STATUS
            ,#{item.createdBy,jdbcType=VARCHAR} AS CREATED_BY
            ,CURRENT_TIMESTAMP AS CREATION_DATE
            ,#{item.lastUpdatedBy,jdbcType=VARCHAR} AS LAST_UPDATED_BY
            ,CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
            
        </foreach>
        ) T
        WHERE 1=1
    </insert>


    <insert id = "insertDefaultRelationRoleUserData" parameterType="com.sevb.system.entity.vo.RelationVO" >
        INSERT INTO public.T_IBDP_RELATION_ROLE_USER (
        ID
        ,ROLE_ID
        ,USER_ID
        ,REMARK
        ,STATUS
        ,CREATION_DATE
        ,CREATED_BY
        ,LAST_UPDATE_DATE
        ,LAST_UPDATED_BY
        )
        WITH TMP_ROLE_T AS (
           SELECT ROLE_ID
           FROM public.T_IBDP_ROLE T
           WHERE 1=1
           AND ROLE_CODE = 'default'
        )
        ,TMP_USER_T AS (
           SELECT USER_ID
           FROM public.T_IBDP_USER T
           WHERE 1=1
           AND USER_CODE = #{userCode}
        )
        SELECT
        concat('relation' ,TO_CHAR(CURRENT_TIMESTAMP,'YYYYMMDD'), CAST(NEXTVAL('PUBLIC.SEQ_DEMO_ID') as VARCHAR) AS ID
        ,ROLE_ID
        ,USER_ID
        ,REMARK
        ,STATUS
        ,CREATION_DATE
        ,CREATED_BY
        ,LAST_UPDATE_DATE
        ,LAST_UPDATED_BY
        FROM (
            SELECT
            ROLE_ID
            ,USER_ID
            ,NULL AS REMARK
            ,'Y' AS STATUS
            ,'-1' AS CREATED_BY
            ,CURRENT_TIMESTAMP AS CREATION_DATE
            ,'-1' AS LAST_UPDATED_BY
            ,CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
            FROM TMP_ROLE_T TA
            INNER JOIN TMP_USER_T TB ON (1=1)
            WHERE 1=1
        ) T
        WHERE 1=1
    </insert>

    <select id="selectRelationRoleMenuData" parameterType="com.sevb.system.entity.vo.RelationVO" resultType="com.sevb.system.entity.vo.RelationVO">
        SELECT
        ID
        ,ROLE_ID
        ,MENU_ID
        ,REMARK
        ,STATUS
        ,CREATION_DATE
        ,CREATED_BY
        ,LAST_UPDATE_DATE
        ,LAST_UPDATED_BY
        FROM public.T_IBDP_RELATION_ROLE_MENU
        WHERE 1=1
        <if test="roleId != null and roleId != ''">
            AND ROLE_ID = #{roleId}
        </if>
        <if test="menuId != null and menuId != ''">
            AND MENU_ID = #{menuId}
        </if>
        <if test="status != null and status != ''">
            AND STATUS = #{status}
        </if>
    </select>

    <delete id="deleteRelationRoleMenuData" parameterType="com.sevb.system.entity.vo.RelationVO">
        DELETE FROM public.T_IBDP_RELATION_ROLE_MENU
        WHERE 1=1
        <if test="roleId != null and roleId != ''">
            AND ROLE_ID = #{roleId}
        </if>
    </delete>

    <insert id = "insertRelationRoleMenuData" parameterType="com.sevb.system.entity.vo.RelationVO" >
        INSERT INTO public.T_IBDP_RELATION_ROLE_MENU (
        ID
        ,ROLE_ID
        ,MENU_ID
        ,REMARK
        ,STATUS
        ,CREATION_DATE
        ,CREATED_BY
        ,LAST_UPDATE_DATE
        ,LAST_UPDATED_BY
        )
        SELECT
        concat('relation' ,TO_CHAR(CURRENT_TIMESTAMP,'YYYYMMDD'), CAST(NEXTVAL('PUBLIC.SEQ_DEMO_ID') as VARCHAR) AS ID
        ,ROLE_ID
        ,MENU_ID
        ,REMARK
        ,STATUS
        ,CREATION_DATE
        ,CREATED_BY
        ,LAST_UPDATE_DATE
        ,LAST_UPDATED_BY
        FROM (
        <foreach collection="records" item="item" separator="UNION ALL" >
            SELECT
            #{item.roleId,jdbcType=VARCHAR} AS ROLE_ID
            ,#{item.menuId,jdbcType=VARCHAR} AS MENU_ID
            ,#{item.remark,jdbcType=VARCHAR} AS REMARK
            ,#{item.status,jdbcType=VARCHAR} AS STATUS
            ,#{item.createdBy,jdbcType=VARCHAR} AS CREATED_BY
            ,CURRENT_TIMESTAMP AS CREATION_DATE
            ,#{item.lastUpdatedBy,jdbcType=VARCHAR} AS LAST_UPDATED_BY
            ,CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
            
        </foreach>
        ) T
        WHERE 1=1
    </insert>

    <select id="selectRelationRolePermissionData" parameterType="com.sevb.system.entity.vo.RelationVO" resultType="com.sevb.system.entity.vo.RelationVO">
        SELECT
        ID
        ,ROLE_ID
        ,PERMISSION_ID
        ,REMARK
        ,STATUS
        ,CREATION_DATE
        ,CREATED_BY
        ,LAST_UPDATE_DATE
        ,LAST_UPDATED_BY
        FROM public.T_IBDP_RELATION_ROLE_PERMISSION
        WHERE 1=1
        <if test="roleId != null and roleId != ''">
            AND ROLE_ID = #{roleId}
        </if>
        <if test="permissionId != null and permissionId != ''">
            AND PERMISSION_ID = #{permissionId}
        </if>
        <if test="status != null and status != ''">
            AND STATUS = #{status}
        </if>
    </select>

    <delete id="deleteRelationRolePermissionData" parameterType="com.sevb.system.entity.vo.RelationVO">
        DELETE FROM public.T_IBDP_RELATION_ROLE_PERMISSION
        WHERE 1=1
        <if test="roleId != null and roleId != ''">
            AND ROLE_ID = #{roleId}
        </if>
    </delete>


    <insert id = "insertRelationRolePermissionData" parameterType="com.sevb.system.entity.vo.RelationVO" >
        INSERT INTO public.T_IBDP_RELATION_ROLE_PERMISSION (
        ID
        ,ROLE_ID
        ,PERMISSION_ID
        ,REMARK
        ,STATUS
        ,CREATION_DATE
        ,CREATED_BY
        ,LAST_UPDATE_DATE
        ,LAST_UPDATED_BY
        )
        SELECT
        concat('relation' ,TO_CHAR(CURRENT_TIMESTAMP,'YYYYMMDD'), CAST(NEXTVAL('PUBLIC.SEQ_DEMO_ID') as VARCHAR) AS ID
        ,ROLE_ID
        ,PERMISSION_ID
        ,REMARK
        ,STATUS
        ,CREATION_DATE
        ,CREATED_BY
        ,LAST_UPDATE_DATE
        ,LAST_UPDATED_BY
        FROM (
        <foreach collection="records" item="item" separator="UNION ALL" >
            SELECT
            #{item.roleId,jdbcType=VARCHAR} AS ROLE_ID
            ,#{item.permissionId,jdbcType=VARCHAR} AS PERMISSION_ID
            ,#{item.remark,jdbcType=VARCHAR} AS REMARK
            ,#{item.status,jdbcType=VARCHAR} AS STATUS
            ,#{item.createdBy,jdbcType=VARCHAR} AS CREATED_BY
            ,CURRENT_TIMESTAMP AS CREATION_DATE
            ,#{item.lastUpdatedBy,jdbcType=VARCHAR} AS LAST_UPDATED_BY
            ,CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
            
        </foreach>
        ) T
        WHERE 1=1
    </insert>

</mapper>