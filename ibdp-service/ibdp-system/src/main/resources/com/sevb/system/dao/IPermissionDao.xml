<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sevb.system.dao.IPermissionDao">
    <select id="selectPermissionData" parameterType="com.sevb.system.entity.vo.PermissionInputVO" resultType="com.sevb.system.entity.vo.PermissionVO">
        SELECT
            PERMISSION_ID
            ,PERMISSION_CODE
            ,PERMISSION_NAME
            ,PERMISSION_DESC
            ,SEGMENT_01
            ,SEGMENT_02
            ,SEGMENT_03
            ,SEGMENT_04
            ,SEGMENT_05
            ,REMARK
            ,STATUS
            ,CREATED_BY
            ,CREATION_DATE
            ,LAST_UPDATED_BY
            ,LAST_UPDATE_DATE
        FROM public.T_IBDP_PERMISSION
        WHERE 1=1
        <if test="permissionId != null and permissionId != ''">
            AND PERMISSION_ID = #{permissionId}
        </if>
        <if test="permissionCode != null and permissionCode != ''">
            AND PERMISSION_CODE = #{permissionCode}
        </if>
        <if test="permissionName != null and permissionName != ''">
            AND PERMISSION_NAME LIKE '%' || #{permissionName} || '%'
        </if>
        <if test="status != null and status != ''">
            AND STATUS = #{status}
        </if>
        <if test="beginDateString != null and beginDateString != ''">
            <![CDATA[
            AND CREATION_DATE >= TO_DATE(#{beginDateString},'YYYY-MM-DD HH24:MI:SS')
            ]]>
        </if>
        <if test="endDateString != null and endDateString != ''">
            <![CDATA[
            AND CREATION_DATE < TO_DATE(#{endDateString},'YYYY-MM-DD HH24:MI:SS')
            ]]>
        </if>
    </select>
    <update id="updatePermissionData" parameterType="com.sevb.system.entity.vo.PermissionVO" >
        update public.T_IBDP_PERMISSION
        set LAST_UPDATE_DATE = CURRENT_TIMESTAMP
        <if test="permissionCode != null and permissionCode != ''">
            ,PERMISSION_CODE = #{permissionCode,jdbcType=VARCHAR}
        </if>
        <if test="permissionName != null and permissionName != ''">
            ,PERMISSION_NAME = #{permissionName,jdbcType=VARCHAR}
        </if>
        <if test="permissionDesc != null and permissionDesc != ''">
            ,PERMISSION_DESC = #{permissionDesc,jdbcType=VARCHAR}
        </if>
        <if test="remark != null and remark != ''">
            ,REMARK = #{remark,jdbcType=VARCHAR}
        </if>
        <if test="status != null and status != ''">
            ,STATUS = #{status,jdbcType=VARCHAR}
        </if>
<!--        <if test="createdBy != null and createdBy != ''">-->
<!--            ,CREATED_BY = #{createdBy,jdbcType=VARCHAR}-->
<!--        </if>-->
<!--        <if test="creationDate != null and creationDate != ''">-->
<!--            ,CREATION_DATE = #{creationDate,jdbcType=VARCHAR}-->
<!--        </if>-->
        <if test="lastUpdatedBy != null and lastUpdatedBy != ''">
            ,LAST_UPDATED_BY = #{lastUpdatedBy,jdbcType=VARCHAR}
        </if>
        where 1=1
        AND PERMISSION_ID = #{permissionId,jdbcType=VARCHAR}
    </update>

    <insert id = "insertPermissionData" parameterType="com.sevb.system.entity.vo.PermissionVO" >
        <selectKey resultType="String" keyProperty="permissionId" order="BEFORE">
            SELECT
            concat('permission' ,TO_CHAR(CURRENT_TIMESTAMP,'YYYYMMDD'), CAST(NEXTVAL('PUBLIC.SEQ_DEMO_ID') as VARCHAR) AS permissionId
            
        </selectKey>
        INSERT INTO public.T_IBDP_PERMISSION (
            PERMISSION_ID
            ,PERMISSION_CODE
            ,PERMISSION_NAME
            ,PERMISSION_DESC
            ,SEGMENT_01
            ,SEGMENT_02
            ,SEGMENT_03
            ,SEGMENT_04
            ,SEGMENT_05
            ,REMARK
            ,STATUS
            ,CREATED_BY
            ,CREATION_DATE
            ,LAST_UPDATED_BY
            ,LAST_UPDATE_DATE
        )
        SELECT
            PERMISSION_ID
            ,PERMISSION_CODE
            ,PERMISSION_NAME
            ,PERMISSION_DESC
            ,SEGMENT_01
            ,SEGMENT_02
            ,SEGMENT_03
            ,SEGMENT_04
            ,SEGMENT_05
            ,REMARK
            ,STATUS
            ,CREATED_BY
            ,CREATION_DATE
            ,LAST_UPDATED_BY
            ,LAST_UPDATE_DATE
        FROM (
           SELECT
         #{permissionId,jdbcType=VARCHAR} AS PERMISSION_ID
        ,#{permissionCode,jdbcType=VARCHAR} AS PERMISSION_CODE
        ,#{permissionName,jdbcType=VARCHAR} AS PERMISSION_NAME
        ,#{permissionDesc,jdbcType=VARCHAR} AS PERMISSION_DESC
        ,#{segment01,jdbcType=VARCHAR} AS SEGMENT_01
        ,#{segment02,jdbcType=VARCHAR} AS SEGMENT_02
        ,#{segment03,jdbcType=VARCHAR} AS SEGMENT_03
        ,#{segment04,jdbcType=VARCHAR} AS SEGMENT_04
        ,#{segment05,jdbcType=VARCHAR} AS SEGMENT_05
        ,#{remark,jdbcType=VARCHAR} AS REMARK
        ,#{status,jdbcType=VARCHAR} AS STATUS
        ,#{createdBy,jdbcType=VARCHAR} AS CREATED_BY
        ,CURRENT_TIMESTAMP AS CREATION_DATE
        ,#{lastUpdatedBy,jdbcType=VARCHAR} AS LAST_UPDATED_BY
        ,CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
           
        ) T
        WHERE 1=1
    </insert>

    <delete id="deletePermissionData" parameterType="com.sevb.system.entity.vo.PermissionInputVO">
        DELETE FROM public.T_IBDP_PERMISSION
        WHERE 1=1
        AND PERMISSION_ID = #{permissionId,jdbcType=VARCHAR}
    </delete>

    <insert id = "insertPermissionDataBatch" parameterType="com.sevb.system.entity.vo.PermissionVO" >
        INSERT INTO public.T_IBDP_PERMISSION (
            PERMISSION_ID
            ,PERMISSION_CODE
            ,PERMISSION_NAME
            ,PERMISSION_DESC
            ,SEGMENT_01
            ,SEGMENT_02
            ,SEGMENT_03
            ,SEGMENT_04
            ,SEGMENT_05
            ,REMARK
            ,STATUS
            ,CREATED_BY
            ,CREATION_DATE
            ,LAST_UPDATED_BY
            ,LAST_UPDATE_DATE
        )
        SELECT
        concat('permission' ,TO_CHAR(CURRENT_TIMESTAMP,'YYYYMMDD'), CAST(NEXTVAL('PUBLIC.SEQ_DEMO_ID') as VARCHAR) AS PERMISSION_ID
        ,PERMISSION_CODE
        ,PERMISSION_NAME
        ,PERMISSION_DESC
        ,SEGMENT_01
        ,SEGMENT_02
        ,SEGMENT_03
        ,SEGMENT_04
        ,SEGMENT_05
        ,REMARK
        ,'Y' AS STATUS
        ,CREATED_BY
        ,CREATION_DATE
        ,LAST_UPDATED_BY
        ,LAST_UPDATE_DATE
        FROM (
        <foreach collection="records" item="item" separator="UNION ALL" >
        SELECT
             #{item.permissionCode,jdbcType=VARCHAR} AS PERMISSION_CODE
            ,#{item.permissionName,jdbcType=VARCHAR} AS PERMISSION_NAME
            ,#{item.permissionDesc,jdbcType=VARCHAR} AS PERMISSION_DESC
            ,#{item.segment01,jdbcType=VARCHAR} AS SEGMENT_01
            ,#{item.segment02,jdbcType=VARCHAR} AS SEGMENT_02
            ,#{item.segment03,jdbcType=VARCHAR} AS SEGMENT_03
            ,#{item.segment04,jdbcType=VARCHAR} AS SEGMENT_04
            ,#{item.segment05,jdbcType=VARCHAR} AS SEGMENT_05
            ,#{item.remark,jdbcType=VARCHAR} AS REMARK
            ,#{item.status,jdbcType=VARCHAR} AS STATUS
            ,#{item.createdBy,jdbcType=VARCHAR} AS CREATED_BY
        ,CURRENT_TIMESTAMP AS CREATION_DATE
        ,#{item.lastUpdatedBy,jdbcType=VARCHAR} AS LAST_UPDATED_BY
        ,CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
        
        </foreach>
        ) T
        WHERE 1=1
    </insert>

    <update id="updatePermissionDataBatch" parameterType="list" >
        <!-- MERGE INTO public.T_IBDP_PERMISSION TA
        USING (
        <foreach collection="records" item="item" separator="UNION ALL" >
            SELECT
             #{item.permissionId,jdbcType=VARCHAR} AS PERMISSION_ID
            ,#{item.permissionCode,jdbcType=VARCHAR} AS PERMISSION_CODE
            ,#{item.permissionName,jdbcType=VARCHAR} AS PERMISSION_NAME
            ,#{item.permissionDesc,jdbcType=VARCHAR} AS PERMISSION_DESC
            ,#{item.segment01,jdbcType=VARCHAR} AS SEGMENT_01
            ,#{item.segment02,jdbcType=VARCHAR} AS SEGMENT_02
            ,#{item.segment03,jdbcType=VARCHAR} AS SEGMENT_03
            ,#{item.segment04,jdbcType=VARCHAR} AS SEGMENT_04
            ,#{item.segment05,jdbcType=VARCHAR} AS SEGMENT_05
            ,#{item.remark,jdbcType=VARCHAR} AS REMARK
            ,#{item.status,jdbcType=VARCHAR} AS STATUS
            ,#{item.createdBy,jdbcType=VARCHAR} AS CREATED_BY
            ,CURRENT_TIMESTAMP AS CREATION_DATE
            ,#{item.lastUpdatedBy,jdbcType=VARCHAR} AS LAST_UPDATED_BY
            ,CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
            
        </foreach>
        ) TB ON (TA.PERMISSION_ID = TB.PERMISSION_ID)
        WHEN MATCHED THEN
        UPDATE SET
         TA.REMARK = TB.REMARK
        ,TA.STATUS = TB.STATUS
        ,TA.LAST_UPDATED_BY = TB.LAST_UPDATED_BY
        ,TA.LAST_UPDATE_DATE = CURRENT_TIMESTAMP -->
    </update>

    <delete id="deletePermissionDataBatch" parameterType="list">
        DELETE FROM public.T_IBDP_PERMISSION
        WHERE 1=1
        AND PERMISSION_ID IN
        <foreach collection="records" item="item" open="(" separator="," close=")">
            (#{item.permissionId})
        </foreach>
    </delete>

</mapper>