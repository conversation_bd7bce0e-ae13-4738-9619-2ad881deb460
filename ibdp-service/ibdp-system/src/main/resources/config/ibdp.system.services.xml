<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:jaxrs="http://cxf.apache.org/jaxrs"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
       http://cxf.apache.org/jaxrs
       http://cxf.apache.org/schemas/jaxrs.xsd">
    <jaxrs:server id="systemServiceRest" address="/system">
        <jaxrs:features>
            <ref bean = "demoFeatures" />
        </jaxrs:features>
        <jaxrs:serviceBeans>
            <ref bean="userService" />
            <ref bean="roleService" />
            <ref bean="permissionService" />
            <ref bean="menuService" />
            <ref bean="classTypeService" />
            <ref bean="classService" />
        </jaxrs:serviceBeans>
        <jaxrs:providers>
            <ref bean="jsonProvider" />
        </jaxrs:providers>
    </jaxrs:server>
    <bean id="userService" class="com.sevb.system.service.impl.UserService" />
    <bean id="roleService" class="com.sevb.system.service.impl.RoleService" />
    <bean id="menuService" class="com.sevb.system.service.impl.MenuService" />
    <bean id="permissionService" class="com.sevb.system.service.impl.PermissionService" />
    <bean id="classTypeService" class="com.sevb.system.service.impl.ClassTypeService" />
    <bean id="classService" class="com.sevb.system.service.impl.ClassService" />
    <bean id="jsonProvider" class="org.codehaus.jackson.jaxrs.JacksonJsonProvider" />
    <bean id="validationProvider" class="org.apache.cxf.validation.BeanValidationProvider" />
    <bean id="validationInInterceptor" class="org.apache.cxf.jaxrs.validation.JAXRSBeanValidationInInterceptor">
        <property name="provider" ref = "validationProvider" />
    </bean>

</beans>
