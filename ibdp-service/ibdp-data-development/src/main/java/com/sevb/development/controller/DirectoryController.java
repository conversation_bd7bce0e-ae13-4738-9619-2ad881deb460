package com.sevb.development.controller;

import com.sevb.common.util.RequestReturnVO;
import com.sevb.development.entity.dto.InsertDirectoryDTO;
import com.sevb.development.entity.dto.UpdateDirectoryDto;
import com.sevb.development.service.IDirectoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags = "主题管理")
@RestController
@RequestMapping("/directory")
public class DirectoryController {

    @Resource
    private IDirectoryService directoryService;

    @PostMapping("/addDirectoryData")
    @ApiOperation(value = "新增主题目录")
    @ApiImplicitParam(name = "form", value = "主题目录表单", required = true, dataType = "InsertDirectoryDTO", dataTypeClass = InsertDirectoryDTO.class)
    public RequestReturnVO addDirectoryData(@NotNull @RequestBody InsertDirectoryDTO form) {
        return directoryService.addDirectoryData(form);
    }

    @PutMapping("/editDirectoryData")
    @ApiOperation(value = "编辑主题目录")
    @ApiImplicitParam(name = "form", value = "主题目录表单", required = true, dataType = "UpdateDirectoryDto", dataTypeClass = UpdateDirectoryDto.class)
    public RequestReturnVO editDirectoryData(@NotNull @RequestBody UpdateDirectoryDto form) {
        return directoryService.editDirectoryData(form);
    }

    @DeleteMapping("/deleteDirectoryData")
    @ApiOperation(value = "删除主题目录")
    @ApiImplicitParam(name = "directoryId", value = "目录ID", required = true, dataType = "Integer", dataTypeClass = Integer.class)
    public RequestReturnVO deleteDirectoryData(@RequestParam(value = "directoryId") Integer directoryId) {
        return directoryService.removeDirectoryData(directoryId);
    }

    @GetMapping("/getDirectoryListByProjectId")
    @ApiOperation(value = "查询主题目录列表")
    public RequestReturnVO getDirectoryListByProjectId(@RequestParam(value = "projectId") String projectId) {
        return directoryService.selectDirectoryByProjectId(projectId);
    }
}
