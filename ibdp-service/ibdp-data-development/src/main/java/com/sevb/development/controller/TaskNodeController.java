package com.sevb.development.controller;

import com.sevb.common.util.RequestReturnVO;
import com.sevb.datax.admin.entity.JobInfo;
import com.sevb.development.entity.dto.ChildTypeDTO;
import com.sevb.development.entity.dto.NodeTypeDTO;
import com.sevb.development.entity.dto.TaskNodeDTO;
import com.sevb.development.entity.vo.UpdateWorkflowEdgesVo;
import com.sevb.development.service.ITaskNodeService;
import com.wugui.datatx.core.biz.model.TriggerParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Api(tags = "任务节点管理")
@RestController
@RequestMapping("/taskNode")
public class TaskNodeController {

    @Resource
    private ITaskNodeService taskNodeService;

    @PostMapping("/addTaskNodeData")
    @ApiOperation(value = "新增任务节点")
    @ApiImplicitParam(name = "form", value = "任务节点表单", required = true, dataType = "TaskNodeDTO", dataTypeClass = TaskNodeDTO.class)
    public RequestReturnVO addTaskNodeData(@NotNull @RequestBody TaskNodeDTO form) {
        return taskNodeService.addTaskNodeData(form);
    }

    @PutMapping("/editTaskNodeData")
    @ApiOperation(value = "编辑任务节点")
    @ApiImplicitParam(name = "form", value = "任务节点表单", required = true, dataType = "TaskNodeDTO", dataTypeClass = TaskNodeDTO.class)
    public RequestReturnVO editTaskNodeData(@NotNull @RequestBody TaskNodeDTO form) {
        return taskNodeService.editTaskNodeData(form);
    }

    @DeleteMapping("/removeTaskNodeData")
    @ApiOperation(value = "删除任务节点")
    @ApiImplicitParam(name = "taskNodeId", value = "任务节点ID", required = true, dataType = "Integer", dataTypeClass = Integer.class)
    public RequestReturnVO removeTaskNodeData(@RequestParam(value = "taskNodeId") Integer taskNodeId) {
        return taskNodeService.removeTaskNodeData(taskNodeId);
    }

    @GetMapping("/selectTaskNodeById")
    @ApiOperation(value = "查询任务节点")
    @ApiImplicitParam(name = "taskNodeId", value = "任务节点ID", required = true, dataType = "Integer", dataTypeClass = Integer.class)
    public RequestReturnVO selectTaskNodeById(@RequestParam(value = "taskNodeId") Integer taskNodeId) {
        return taskNodeService.selectTaskNodeById(taskNodeId);
    }

    @GetMapping("/getTaskNodeListByWorkflowId")
    @ApiOperation(value = "根据工作流ID查询任务节点列表")
    @ApiImplicitParam(name = "workflowId", value = "工作流ID", required = true, dataType = "Integer", dataTypeClass = Integer.class)
    public RequestReturnVO getTaskNodeListByWorkflowId(@RequestParam(value = "workflowId") Integer workflowId) {
        return taskNodeService.getTaskNodeListByWorkflowId(workflowId);
    }

    @GetMapping("/nodeTypeTree")
    @ApiOperation(value = "查询任务节点类型树")
    public RequestReturnVO getNodeTypeTree() {
        return taskNodeService.getNodeTypeTree();
    }

    @PostMapping("/updateWorkflowEdges")
    @ApiOperation(value = "更新工作流连线")
    @ApiImplicitParam(name = "vo", value = "工作流连线表单", required = true, dataType = "UpdateWorkflowEdgesVo", dataTypeClass = UpdateWorkflowEdgesVo.class)
    public RequestReturnVO updateWorkflowEdges(@NotNull @RequestBody UpdateWorkflowEdgesVo vo) {
        return taskNodeService.updateWorkflowEdges(vo);
    }

    @GetMapping("/getNodeExecuteHistory")
    @ApiOperation(value = "查询节点执行历史")

    @ApiImplicitParams({
            @ApiImplicitParam(name = "nodeId", value = "节点ID", required = true, dataType = "Integer", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "pageNum", defaultValue = "1", value = "当前页数 ", dataType = "Integer", dataTypeClass = String.class),
            @ApiImplicitParam(name = "pageSize", defaultValue = "10", value = "每页行数", dataType = "Integer", dataTypeClass = String.class)
    })
    public RequestReturnVO getNodeExecuteHistory(@RequestParam(value = "nodeId") Integer nodeId,
                                                 @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                 @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        return taskNodeService.getNodeExecuteHistory(nodeId, pageNum, pageSize);
    }

    @GetMapping("/getTaskLogDetailCat")
    @ApiOperation(value = "查询任务节点执行日志详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "logId", value = "日志ID", required = true, dataType = "Integer", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "executorAddress", value = "执行器地址", required = true, dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "triggerTime", value = "触发时间", required = true, dataType = "Long", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "fromLineNum", value = "从第几行开始", required = true, dataType = "Integer", dataTypeClass = Integer.class)
    })
    public RequestReturnVO getTaskLogDetailCat(@RequestParam(value = "logId") Integer logId,
                                               @RequestParam(value = "executorAddress") String executorAddress,
                                               @RequestParam(value = "triggerTime") Long triggerTime,
                                               @RequestParam(value = "fromLineNum") Integer fromLineNum) {
        return taskNodeService.getTaskLogDetailCat(logId, executorAddress, triggerTime, fromLineNum);
    }

    @GetMapping("/getNodeTaskList")
    @ApiOperation(value = "查询节点任务列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "nodeId", value = "节点ID", dataType = "Integer", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "pic", value = "责任人", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "cdsId", value = "数据源ID", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "dsType", value = "任务类型", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "pageNum", defaultValue = "1", value = "当前页数 ", dataType = "Integer", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "pageSize", defaultValue = "10", value = "每页行数", dataType = "Integer", dataTypeClass = Integer.class)
    })
    public RequestReturnVO getNodeTaskList(@RequestParam(value = "nodeId", required = false) Integer nodeId,
                                           @RequestParam(value = "pic", required = false) String pic,
                                           @RequestParam(value = "cdsId", required = false) String cdsId,
                                           @RequestParam(value = "dsType", required = false) String dsType,
                                           @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                           @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        return taskNodeService.getNodeTaskList(nodeId, pic, cdsId, dsType, pageNum, pageSize);
    }

    @PutMapping("/online")
    @ResponseBody
    @ApiOperation(value = "上线任务节点")
    @ApiImplicitParam(name = "jobId", value = "任务ID", required = true, dataType = "Integer", dataTypeClass = Integer.class)
    public RequestReturnVO onlineNodeTaskJob(@RequestParam(value = "jobId") Integer jobId) {
        return this.taskNodeService.onlineNodeTaskJob(jobId);
    }

    @PutMapping("/offline")
    @ResponseBody
    @ApiOperation(value = "下线任务节点")
    @ApiImplicitParam(name = "jobId", value = "任务ID", required = true, dataType = "Integer", dataTypeClass = Integer.class)
    public RequestReturnVO offlineNodeTaskJob(@RequestParam(value = "jobId") Integer jobId) {
        return this.taskNodeService.offlineNodeTaskJob(jobId);
    }

    @PutMapping("/trigger")
    @ResponseBody
    @ApiOperation(value = "手动触发任务节点")
    @ApiImplicitParam(name = "jobId", value = "任务ID", required = true, dataType = "Integer", dataTypeClass = Integer.class)
    public RequestReturnVO triggerManualProcess(@RequestParam(value = "jobId") Integer jobId) {
        return this.taskNodeService.triggerManualProcess(jobId);
    }

    @GetMapping("/getTaskTypeList")
    @ResponseBody
    @ApiOperation(value = "查询任务类型")
    public RequestReturnVO getTaskTypeList() {
        RequestReturnVO resp = taskNodeService.getNodeTypeTree();
        if (!resp.getStatus().equals("200") || resp.getResultObjVO() == null) {
            return RequestReturnVO.success();
        }

        @SuppressWarnings("unchecked")
        List<NodeTypeDTO> tree = (List<NodeTypeDTO>) resp.getResultObjVO();
        List<Map<String, String>> result = tree.stream().flatMap(parent -> {
            List<ChildTypeDTO> children = parent.getChildren();
            return children == null ? Stream.empty() : children.stream();
        }).map(child -> {
            Map<String, String> map = new HashMap<>(4);
            map.put("taskType", child.getChildType());
            map.put("taskName", child.getChildName());
            return map;
        }).collect(Collectors.toList());
        return RequestReturnVO.success(result);
    }


    @PutMapping("/runNodeTaskJob")
    @ResponseBody
    @ApiOperation(value = "运行数据采集任务")
    @ApiImplicitParam(name = "taskNodeDto", value = "任务节点表单", required = true, dataType = "TaskNodeDTO", dataTypeClass = TaskNodeDTO.class)
    public RequestReturnVO runNodeTaskJob(@RequestBody TaskNodeDTO taskNodeDto) {
        return this.taskNodeService.runNodeTaskJob(taskNodeDto);
    }
}
