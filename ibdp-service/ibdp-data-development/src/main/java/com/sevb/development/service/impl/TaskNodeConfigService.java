package com.sevb.development.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.sevb.common.util.BeanUtils;
import com.sevb.common.util.RequestReturnVO;
import com.sevb.common.util.SqlParseUtils;
import com.sevb.datax.admin.entity.JobInfo;
import com.sevb.datax.admin.service.JobService;
import com.sevb.development.dao.ITaskNodeConfigDao;
import com.sevb.development.dao.ITaskNodeDao;
import com.sevb.development.entity.dto.InsertNodeConfigDTO;
import com.sevb.development.entity.enums.ChildTypeEnum;
import com.sevb.development.entity.enums.NodeTypeEnum;
import com.sevb.development.entity.vo.TaskNodeConfigVO;
import com.sevb.development.entity.vo.TaskNodeVO;
import com.sevb.development.service.ITaskNodeConfigService;
import com.wugui.datatx.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.sevb.development.builder.JobConvert.getJobInfo;

@Service
@Slf4j
public class TaskNodeConfigService implements ITaskNodeConfigService {

    @Resource
    private ITaskNodeConfigDao taskNodeConfigDao;
    @Resource
    private ITaskNodeDao taskNodeDao;
    @Resource
    private JobService jobService;

    /**
     * 根据任务节点ID查找当前节点配置信息
     *
     * @param nodeId 任务节点ID
     * @return com.sevb.common.util.RequestReturnVO
     */
    @Override
    public RequestReturnVO getActiveDataByNodeId(Integer nodeId) {
        TaskNodeConfigVO taskNodeConfigVO = taskNodeConfigDao.getActiveDataByNodeId(nodeId);
        if (null == taskNodeConfigVO) {
            TaskNodeVO taskNodeVO = taskNodeDao.selectTaskNodeById(nodeId);
            if (taskNodeVO != null) {
                taskNodeConfigVO = new TaskNodeConfigVO();
                BeanUtils.copyProperties(taskNodeVO, taskNodeConfigVO);
            } else {
                return RequestReturnVO.fail("Task node not found");
            }
        }
        // 枚举值转换
        taskNodeConfigVO.setChildType(ChildTypeEnum.fromCode(taskNodeConfigVO.getChildType()).getName());
        taskNodeConfigVO.setNodeType(NodeTypeEnum.fromCode(taskNodeConfigVO.getNodeType()).getName());
        // jvm参数解析
        BeanUtils.copyProperties(parseSparkSubmitCommand(taskNodeConfigVO), taskNodeConfigVO);
        return RequestReturnVO.success(taskNodeConfigVO);
    }

    /**
     * 根据任务节点ID和版本号查找节点配置信息
     *
     * @param nodeId  任务节点ID
     * @param version 版本号
     * @return com.sevb.common.util.RequestReturnVO
     */
    @Override
    public RequestReturnVO getTaskNodeConfigByUnique(Integer nodeId, Integer version) {
        TaskNodeConfigVO taskNodeConfigVO = taskNodeConfigDao.getTaskNodeConfigByUnique(nodeId, version);
        if (null != taskNodeConfigVO) {
            taskNodeConfigVO.setChildType(ChildTypeEnum.fromCode(taskNodeConfigVO.getChildType()).getName());
            taskNodeConfigVO.setNodeType(NodeTypeEnum.fromCode(taskNodeConfigVO.getNodeType()).getName());
            // jvm参数解析
            BeanUtils.copyProperties(parseSparkSubmitCommand(taskNodeConfigVO), taskNodeConfigVO);
        }
        return RequestReturnVO.success(taskNodeConfigVO);
    }

    /**
     * 新增任务节点配置信息
     *
     * @param dto 任务节点配置信息表单
     * @return com.sevb.common.util.RequestReturnVO
     */
    @Override
    public RequestReturnVO insertTaskNodeConfig(InsertNodeConfigDTO dto) {
        TaskNodeVO node = taskNodeDao.selectTaskNodeById(dto.getNodeId());
        if (null == node) {
            return RequestReturnVO.fail("Task node not found");
        }
        TaskNodeConfigVO form = new TaskNodeConfigVO();
        BeanUtils.copyProperties(dto, form);
        form.setNodeName(node.getNodeName());
        if (node.getChildType().equals(ChildTypeEnum.Spark.getType())) {
            //jvm参数生成
            form.setJvmParam(buildSparkSubmitCommand(dto));
        }
        int result = taskNodeConfigDao.insertTaskNodeConfig(form);
        taskNodeConfigDao.resetActiveVersion(form.getNodeId(), form.getVersion());
        return RequestReturnVO.success(result > 0);
    }


    /**
     * 根据节点ID获取节点的版本历史
     *
     * @param nodeId 任务节点ID
     * @return com.sevb.common.util.RequestReturnVO
     */
    @Override
    public RequestReturnVO findListByNodeId(Integer nodeId) {
        List<TaskNodeConfigVO> taskNodeConfigVOList = taskNodeConfigDao.findListByNodeId(nodeId);
        taskNodeConfigVOList.forEach(taskNodeConfigVO -> {
            //jvm参数解析
            BeanUtils.copyProperties(parseSparkSubmitCommand(taskNodeConfigVO), taskNodeConfigVO);
            //枚举值转换
            taskNodeConfigVO.setChildType(ChildTypeEnum.fromCode(taskNodeConfigVO.getChildType()).getName());
            taskNodeConfigVO.setNodeType(NodeTypeEnum.fromCode(taskNodeConfigVO.getNodeType()).getName());
        });
        return RequestReturnVO.success(taskNodeConfigVOList);
    }

    /**
     * 还原任务节点的配置版本
     *
     * @param nodeId        任务节点ID
     * @param activeVersion 待还原的版本号
     * @return com.sevb.common.util.RequestReturnVO
     */
    @Override
    public RequestReturnVO restoreTaskNodeConfig(Integer nodeId, Integer activeVersion) {
        TaskNodeConfigVO taskNodeConfigVO = taskNodeConfigDao.getTaskNodeConfigByUnique(nodeId, activeVersion);
        if (null == taskNodeConfigVO) {
            return RequestReturnVO.fail(String.format("还原异常，不存在版本号%d的记录", activeVersion));
        }
        int result = taskNodeConfigDao.resetActiveVersion(nodeId, activeVersion);
        return RequestReturnVO.success(result > 0);
    }

    /**
     * 提交任务节点配置信息
     *
     * @param nodeId 任务节点ID
     * @return com.sevb.common.util.RequestReturnVO
     */
    @Override
    public RequestReturnVO submitTaskNodeConfig(Integer nodeId) {
        TaskNodeConfigVO configVO = taskNodeConfigDao.getActiveDataByNodeId(nodeId);
        if (null == configVO) {
            return RequestReturnVO.fail("任务节点配置信息不存在");
        }
        if (configVO.getSchedule() == null) return RequestReturnVO.fail("调度计划不能为空");
        TaskNodeVO taskNodeVO = taskNodeDao.selectTaskNodeById(nodeId);
        // 校验节点内容
        if (null == configVO.getContent()) {
            return RequestReturnVO.fail("任务节点内容不能为空");
        } else if (configVO.getNodeType().equals(NodeTypeEnum.DataCalculation.getType()) && configVO.getChildType().equals(ChildTypeEnum.SQL.getType()) && !SqlParseUtils.validateSQL(configVO.getContent())) {
            return RequestReturnVO.fail("SQL语法错误");
        }
        JobInfo jobInfo = getJobInfo(configVO, taskNodeVO);
        // 检查节点任务是否存在，存在则更新，不存在则新增
        ReturnT<String> result;
        if (null != taskNodeVO.getJobId()) {
            jobInfo.setId(taskNodeVO.getJobId());
            result = jobService.update(jobInfo);
        } else {
            result = jobService.add(jobInfo);
            Integer dataxJobId = Integer.valueOf(result.getContent());
            TaskNodeVO taskNodeVo = new TaskNodeVO();
            taskNodeVo.setNodeId(nodeId);
            taskNodeVo.setJobId(dataxJobId);
            taskNodeDao.updateTaskNodeData(taskNodeVo);
        }
        return result.getCode() == ReturnT.SUCCESS_CODE ?
                RequestReturnVO.success() : RequestReturnVO.fail(result.getMsg());
    }

    public static String buildSparkSubmitCommand(InsertNodeConfigDTO params) {
        return String.format(
                "--master yarn --deploy-mode client " +
                        "--driver-cores %s --driver-memory %s " +
                        "--num-executors %s --executor-cores %s --executor-memory %s ",
                params.getDriverCores() != null ? params.getDriverCores() : "1",
                params.getDriverMemory() != null ? params.getDriverMemory() : "512M",
                params.getNumExecutors() != null ? params.getNumExecutors() : "2",
                params.getExecutorCores() != null ? params.getExecutorCores() : "2",
                params.getExecutorMemory() != null ? params.getExecutorMemory() : "2G"
        );
    }

    public static TaskNodeConfigVO parseSparkSubmitCommand(TaskNodeConfigVO vo) {
        // 正则匹配提取参数
        String command = vo.getJvmParam();
        if (command != null) {
            extractParam(command, "--driver-cores (\\S+)", matcher -> vo.setDriverCores(matcher.group(1)));
            extractParam(command, "--driver-memory (\\S+)", matcher -> vo.setDriverMemory(matcher.group(1)));
            extractParam(command, "--num-executors (\\S+)", matcher -> vo.setNumExecutors(matcher.group(1)));
            extractParam(command, "--executor-cores (\\S+)", matcher -> vo.setExecutorCores(matcher.group(1)));
            extractParam(command, "--executor-memory (\\S+)", matcher -> vo.setExecutorMemory(matcher.group(1)));
        }
        return vo;
    }

    private static void extractParam(String input, String regex, java.util.function.Consumer<Matcher> action) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            action.accept(matcher);
        }
    }
}
