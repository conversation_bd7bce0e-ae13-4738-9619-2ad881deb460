package com.sevb.development.service.impl;

import com.sevb.common.util.BeanUtils;
import com.sevb.common.util.RequestReturnVO;
import com.sevb.development.dao.IDirectoryDao;
import com.sevb.development.dao.IWorkflowDao;
import com.sevb.development.entity.dto.InsertWorkflowDTO;
import com.sevb.development.entity.dto.UpdateWorkflowDTO;
import com.sevb.development.entity.po.DirectoryPO;
import com.sevb.development.entity.vo.WorkflowVO;
import com.sevb.development.service.IWorkFlowService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Service
public class WorkFlowServiceImpl implements IWorkFlowService {

    @Resource
    private IWorkflowDao workflowDao;

    @Resource
    private IDirectoryDao directoryDao;

    @Override
    public RequestReturnVO addWorkFlowData(InsertWorkflowDTO dto) {
        Integer directoryId = dto.getDirectoryId();
        DirectoryPO directoryData = directoryDao.selectDirectoryById(directoryId);
        if (directoryData == null) {
            return RequestReturnVO.fail("目录ID不存在");
        }
        WorkflowVO vo = new WorkflowVO();
        BeanUtils.copyProperties(dto, vo);
        int result = workflowDao.insertWorkflowData(vo);
        return result > 0 ? RequestReturnVO.success(vo.getWorkflowId()) : RequestReturnVO.fail("新增失败");
    }

    @Override
    public RequestReturnVO editWorkFlowData(UpdateWorkflowDTO dto) {
        if (dto.getDirectoryId() != null && dto.getDirectoryId() != 0) {
            DirectoryPO directoryData = directoryDao.selectDirectoryById(dto.getDirectoryId());
            if (directoryData == null) {
                return RequestReturnVO.fail("目录ID不存在");
            }
        }
        WorkflowVO vo = new WorkflowVO();
        BeanUtils.copyProperties(dto, vo);
        int result = workflowDao.updateWorkflowData(vo);
        return result > 0 ? RequestReturnVO.success() : RequestReturnVO.fail("修改失败");
    }

    @Override
    public RequestReturnVO removeWorkFlowData(Integer workflowId) {
        // 检查是否有子节点
        if (!workflowDao.selectTaskNodesByWorkflowIds(Collections.singletonList(workflowId)).isEmpty()) {
            return RequestReturnVO.fail("该工作流下有任务节点，不能删除");
        }
        int result = workflowDao.deleteWorkflowData(workflowId);
        return result > 0 ? RequestReturnVO.success() : RequestReturnVO.fail("删除失败");
    }

    @Override
    public RequestReturnVO selectWorkFlowById(Integer workflowId) {
        return RequestReturnVO.success(workflowDao.selectWorkflowByWorkflowId(workflowId));
    }
}
