package com.sevb.development.controller;

import com.sevb.common.util.RequestReturnVO;
import com.sevb.development.entity.dto.InsertWorkflowDTO;
import com.sevb.development.entity.dto.UpdateWorkflowDTO;
import com.sevb.development.service.IWorkFlowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/workflow")
@Api(tags = "工作流管理")
public class WorkFlowController {

    @Resource
    private IWorkFlowService workFlowService;

    @PostMapping("/addWorkFlowData")
    @ApiOperation(value = "新增工作流")
    @ApiImplicitParam(name = "form", value = "工作流表单", required = true, dataType = "InsertWorkflowDTO", dataTypeClass = InsertWorkflowDTO.class)
    public RequestReturnVO addWorkFlowData(@NotNull @RequestBody InsertWorkflowDTO form) {
        return workFlowService.addWorkFlowData(form);
    }

    @PutMapping("/editWorkFlowData")
    @ApiOperation(value = "编辑工作流及配置信息")
    @ApiImplicitParam(name = "form", value = "工作流表单", required = true, dataType = "UpdateWorkflowDTO", dataTypeClass = UpdateWorkflowDTO.class)
    public RequestReturnVO editWorkFlowData(@NotNull @RequestBody UpdateWorkflowDTO form) {
        return workFlowService.editWorkFlowData(form);
    }

    @DeleteMapping("/deleteWorkFlowData")
    @ApiOperation(value = "删除工作流")
    @ApiImplicitParam(name = "workflowId", value = "工作流ID", required = true, dataType = "Integer", dataTypeClass = Integer.class)
    public RequestReturnVO deleteWorkFlowData(@RequestParam(value = "workflowId") Integer workflowId) {
        return workFlowService.removeWorkFlowData(workflowId);
    }

    @GetMapping("/selectWorkFlowConfigById")
    @ApiOperation(value = "查询工作流配置")
    @ApiImplicitParam(name = "workflowId", value = "工作流ID", required = true, dataType = "Integer", dataTypeClass = Integer.class)
    public RequestReturnVO selectWorkFlowConfigById(@RequestParam(value = "workflowId") Integer workflowId) {
        return workFlowService.selectWorkFlowById(workflowId);
    }
}
