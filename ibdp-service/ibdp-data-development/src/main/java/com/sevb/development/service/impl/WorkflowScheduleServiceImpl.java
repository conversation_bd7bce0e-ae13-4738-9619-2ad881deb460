package com.sevb.development.service.impl;

import com.alibaba.fastjson.JSON;
import com.sevb.common.util.RequestReturnVO;
import com.sevb.datax.admin.entity.JobInfo;
import com.sevb.datax.admin.mapper.JobInfoMapper;
import com.sevb.datax.admin.service.JobService;
import com.sevb.datax.admin.core.thread.JobTriggerPoolHelper;
import com.sevb.datax.admin.core.trigger.TriggerTypeEnum;
import com.sevb.development.dao.IWorkflowDao;
import com.sevb.development.dao.ITaskNodeDao;
import com.sevb.development.dao.ITaskNodeConfigDao;
import com.sevb.development.entity.po.WorkflowPO;
import com.sevb.development.entity.po.TaskNodePO;
import com.sevb.development.entity.vo.TaskNodeVO;
import com.sevb.development.entity.vo.TaskNodeConfigVO;
import com.sevb.development.service.IWorkflowScheduleService;
import com.wugui.datatx.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.sevb.development.builder.JobConvert.getJobInfo;

/**
 * 工作流调度服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Slf4j
@Service
public class WorkflowScheduleServiceImpl implements IWorkflowScheduleService {

    @Resource
    private JobService jobService;

    @Resource
    private JobInfoMapper jobInfoMapper;

    @Resource
    private IWorkflowDao workflowDao;

    @Resource
    private ITaskNodeDao taskNodeDao;

    @Resource
    private ITaskNodeConfigDao taskNodeConfigDao;

    // 默认执行器组ID - 需要根据实际情况配置
    private static final int DEFAULT_JOB_GROUP = 1;

    @Override
    @Transactional
    public RequestReturnVO createWorkflowScheduleJob(Integer workflowId) {
        // String jobCron, String jobDesc,String triggerUser, Map<String, Object> triggerParams
        try {
            log.info("为工作流创建调度任务，workflowId: {}", workflowId);

            // 1. 检查工作流是否存在
            WorkflowPO workflow = workflowDao.selectWorkflowByWorkflowId(workflowId);
            if (workflow == null) {
                return RequestReturnVO.fail("工作流不存在");
            }
            String jobCron = workflow.getSchedule();
            String jobDesc = workflow.getWorkflowName();
            String triggerUser = workflow.getResponsiblePerson();
            Map<String, Object> triggerParams = new HashMap<>();

            if (jobCron == null || jobCron.trim().isEmpty()) {
                return RequestReturnVO.fail("工作流没有配置调度计划");
            }
            // 2. 检查是否已有调度任务，如果存在则获取现有任务信息
            RequestReturnVO existingJobResult = getWorkflowScheduleJob(workflowId);
            JobInfo existingWorkflowJob = null;
            boolean isUpdate = false;

            if ("200".equals(existingJobResult.getStatus())) {
                existingWorkflowJob = (JobInfo) existingJobResult.getResultObjVO();
                isUpdate = true;
                log.info("工作流已存在调度任务，将进行更新操作，jobId: {}", existingWorkflowJob.getId());
            }

            // 3. 获取工作流下的所有节点
            List<TaskNodeVO> taskNodes = taskNodeDao.selectTaskNodesByWorkflowId(workflowId);
            if (taskNodes == null || taskNodes.isEmpty()) {
                return RequestReturnVO.fail("工作流下没有任务节点");
            }

            List<Integer> createdJobIds = new ArrayList<>();

            // 4. 批量获取节点配置信息
            List<Integer> nodeIds = taskNodes.stream().map(TaskNodeVO::getNodeId).collect(Collectors.toList());
            List<TaskNodeConfigVO> configList = taskNodeConfigDao.findActiveListByNodeIds(nodeIds);
            Map<Integer, TaskNodeConfigVO> configMap = configList.stream()
                    .collect(Collectors.toMap(TaskNodeConfigVO::getNodeId, config -> config));

            // 5. 准备批量操作的数据
            List<JobInfo> jobsToAdd = new ArrayList<>();
            List<JobInfo> jobsToUpdate = new ArrayList<>();
            List<TaskNodeVO> nodesToUpdate = new ArrayList<>();
            Map<Integer, TaskNodeVO> nodeMap = taskNodes.stream()
                    .collect(Collectors.toMap(TaskNodeVO::getNodeId, node -> node));

            // 6. 构建JobInfo对象
            for (TaskNodeVO taskNode : taskNodes) {
                TaskNodeConfigVO configVO = configMap.get(taskNode.getNodeId());
                if (configVO == null) {
                    return RequestReturnVO.fail("节点 {} 没有配置信息，无法创建调度任务", taskNode.getNodeName());
                }

                // 如果节点没有调度计划，使用工作流的调度计划
                if (configVO.getSchedule() == null || configVO.getSchedule().trim().isEmpty()) {
                    configVO.setSchedule(jobCron);
                }

                // 使用JobConvert转换为JobInfo
                JobInfo nodeJobInfo = getJobInfo(configVO, taskNode);

                // 分类处理：新增或更新
                if (taskNode.getJobId() != null) {
                    nodeJobInfo.setId(taskNode.getJobId());
                    jobsToUpdate.add(nodeJobInfo);
                } else {
                    jobsToAdd.add(nodeJobInfo);
                }
            }

            // 7. 批量处理新增任务
            if (!jobsToAdd.isEmpty()) {
                for (JobInfo jobInfo : jobsToAdd) {
                    ReturnT<String> result = jobService.add(jobInfo);
                    if (ReturnT.SUCCESS_CODE == result.getCode()) {
                        Integer dataxJobId = Integer.valueOf(result.getContent());
                        createdJobIds.add(dataxJobId);

                        // 准备批量更新节点的jobId
                        Integer nodeId = extractNodeIdFromJobInfo(jobInfo);
                        if (nodeId != null) {
                            TaskNodeVO updateNode = new TaskNodeVO();
                            updateNode.setNodeId(nodeId);
                            updateNode.setJobId(dataxJobId);
                            nodesToUpdate.add(updateNode);
                        }
                    } else {
                        Integer nodeId = extractNodeIdFromJobInfo(jobInfo);
                        String nodeName = nodeId != null && nodeMap.containsKey(nodeId) ?
                                nodeMap.get(nodeId).getNodeName() : "未知节点";
                        log.error("为节点 {} 创建调度任务失败: {}", nodeId, result.getMsg());
                        return RequestReturnVO.fail("为节点 " + nodeName + " 创建调度任务失败: " + result.getMsg());
                    }
                }
            }

            // 8. 批量处理更新任务
            if (!jobsToUpdate.isEmpty()) {
                for (JobInfo jobInfo : jobsToUpdate) {
                    ReturnT<String> result = jobService.update(jobInfo);
                    if (ReturnT.SUCCESS_CODE == result.getCode()) {
                        createdJobIds.add(jobInfo.getId());
                    } else {
                        Integer nodeId = extractNodeIdFromJobInfo(jobInfo);
                        String nodeName = nodeId != null && nodeMap.containsKey(nodeId) ?
                                nodeMap.get(nodeId).getNodeName() : "未知节点";
                        log.error("为节点 {} 更新调度任务失败: {}", nodeId, result.getMsg());
                        return RequestReturnVO.fail("为节点 " + nodeName + " 更新调度任务失败: " + result.getMsg());
                    }
                }
            }

            // 9. 批量更新节点的jobId
            if (!nodesToUpdate.isEmpty()) {
                int updateCount = taskNodeDao.batchUpdateTaskNodes(
                        nodesToUpdate.stream()
                                .map(node -> {
                                    TaskNodePO po = new TaskNodePO();
                                    po.setNodeId(node.getNodeId());
                                    po.setJobId(node.getJobId());
                                    return po;
                                })
                                .collect(Collectors.toList())
                );
                log.info("批量更新节点jobId完成，更新数量: {}", updateCount);
            }

            // 5. 创建或更新工作流主调度任务（ds_type为workflow）
            JobInfo workflowJobInfo;
            if (isUpdate) {
                // 更新现有任务
                workflowJobInfo = existingWorkflowJob;
                workflowJobInfo.setJobCron(jobCron);
                workflowJobInfo.setPic(triggerUser);
                workflowJobInfo.setJobDesc(jobDesc != null ? jobDesc : "工作流调度任务: " + workflow.getWorkflowName());

                // 确保更新时必填字段不为空
                if (workflowJobInfo.getProjectId() == 0) {
                    workflowJobInfo.setProjectId(1); // 设置默认项目ID
                }
                if (workflowJobInfo.getUserId() == 0) {
                    workflowJobInfo.setUserId(1); // 设置默认用户ID
                }
                if (workflowJobInfo.getJobName() == null || workflowJobInfo.getJobName().trim().isEmpty()) {
                    workflowJobInfo.setJobName("WORKFLOW_" + workflowId + "_" + workflow.getWorkflowName());
                }
                if (workflowJobInfo.getExecutorHandler() == null || workflowJobInfo.getExecutorHandler().trim().isEmpty()) {
                    workflowJobInfo.setExecutorHandler("WorkflowExecutorJobHandler");
                }
                if (workflowJobInfo.getGlueType() == null || workflowJobInfo.getGlueType().trim().isEmpty()) {
                    workflowJobInfo.setGlueType("BEAN");
                }
                if (workflowJobInfo.getExecutorRouteStrategy() == null || workflowJobInfo.getExecutorRouteStrategy().trim().isEmpty()) {
                    workflowJobInfo.setExecutorRouteStrategy("FIRST");
                }
                if (workflowJobInfo.getExecutorBlockStrategy() == null || workflowJobInfo.getExecutorBlockStrategy().trim().isEmpty()) {
                    workflowJobInfo.setExecutorBlockStrategy("SERIAL_EXECUTION");
                }
            } else {
                // 创建新任务
                workflowJobInfo = new JobInfo();
                workflowJobInfo.setJobGroup(DEFAULT_JOB_GROUP);
                workflowJobInfo.setJobName("WORKFLOW_" + workflowId + "_" + workflow.getWorkflowName());
                workflowJobInfo.setExecutorHandler("WorkflowExecutorJobHandler");
                workflowJobInfo.setGlueType("BEAN");
                workflowJobInfo.setExecutorRouteStrategy("FIRST");
                workflowJobInfo.setExecutorBlockStrategy("SERIAL_EXECUTION");
                workflowJobInfo.setExecutorTimeout(0);
                workflowJobInfo.setExecutorFailRetryCount(0);
                workflowJobInfo.setTriggerStatus(0); // 默认停止状态
                workflowJobInfo.setUserId(1); // 默认用户ID
                workflowJobInfo.setPic(triggerUser);    // 责任人
                workflowJobInfo.setProjectId(1); // 设置默认项目ID
                workflowJobInfo.setDsType("workflow"); // 设置为workflow类型
                workflowJobInfo.setJobCron(jobCron);
                workflowJobInfo.setJobDesc(jobDesc != null ? jobDesc : "工作流调度任务: " + workflow.getWorkflowName());
            }

            // 6. 构造jobJson参数
            Map<String, Object> jobJsonMap = new HashMap<>();
            jobJsonMap.put("workflowId", workflowId);
            jobJsonMap.put("triggerUser", triggerUser);
            jobJsonMap.put("triggerParams", triggerParams);
            jobJsonMap.put("nodeJobIds", createdJobIds); // 包含所有节点的jobId
            workflowJobInfo.setJobJson(JSON.toJSONString(jobJsonMap));

            // 7. 保存或更新工作流JobInfo
            ReturnT<String> workflowResult;
            if (isUpdate) {
                workflowResult = jobService.update(workflowJobInfo);
                log.info("更新工作流调度任务，jobId: {}", workflowJobInfo.getId());
            } else {
                workflowResult = jobService.add(workflowJobInfo);
                log.info("创建工作流调度任务");
            }

            if (ReturnT.SUCCESS_CODE != workflowResult.getCode()) {
                String operation = isUpdate ? "更新" : "创建";
                log.error("{}工作流调度任务失败: {}", operation, workflowResult.getMsg());
                return RequestReturnVO.fail(operation + "工作流调度任务失败: " + workflowResult.getMsg());
            }

            Map<String, Object> resultData = new HashMap<>();
            resultData.put("workflowJobId", workflowJobInfo.getId());
            resultData.put("nodeJobIds", createdJobIds);
            resultData.put("totalNodes", taskNodes.size());
            resultData.put("processedNodes", createdJobIds.size());
            resultData.put("isUpdate", isUpdate);

            String operation = isUpdate ? "更新" : "创建";
            log.info("工作流调度任务{}成功，workflowJobId: {}, 处理节点任务数: {}",
                    operation, workflowJobInfo.getId(), createdJobIds.size());
            return RequestReturnVO.success(resultData);

        } catch (Exception e) {
            log.error("创建工作流调度任务异常", e);
            return RequestReturnVO.fail("创建工作流调度任务异常: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public RequestReturnVO updateWorkflowScheduleJob(Integer jobId, String jobCron, String jobDesc,
                                                     Map<String, Object> triggerParams) {
        try {
            log.info("更新工作流调度任务，jobId: {}, jobCron: {}", jobId, jobCron);

            // 1. 获取现有JobInfo
            JobInfo existingJob = jobInfoMapper.loadById(jobId);
            if (existingJob == null) {
                return RequestReturnVO.fail("调度任务不存在");
            }

            // 2. 解析现有的jobJson
            Map<String, Object> jobJsonMap = JSON.parseObject(existingJob.getJobJson(), Map.class);
            if (triggerParams != null) {
                jobJsonMap.put("triggerParams", triggerParams);
            }

            // 3. 更新JobInfo
            existingJob.setJobCron(jobCron);
            if (jobDesc != null) {
                existingJob.setJobDesc(jobDesc);
            }
            existingJob.setJobJson(JSON.toJSONString(jobJsonMap));
            existingJob.setUpdateTime(new Date());

            // 4. 保存更新
            ReturnT<String> updateResult = jobService.update(existingJob);
            if (ReturnT.SUCCESS_CODE != updateResult.getCode()) {
                log.error("更新工作流调度任务失败: {}", updateResult.getMsg());
                return RequestReturnVO.fail("更新调度任务失败: " + updateResult.getMsg());
            }

            log.info("工作流调度任务更新成功，jobId: {}", jobId);
            return RequestReturnVO.success(true);

        } catch (Exception e) {
            log.error("更新工作流调度任务异常", e);
            return RequestReturnVO.fail("更新工作流调度任务异常: " + e.getMessage());
        }
    }

    @Override
    public RequestReturnVO startWorkflowSchedule(Integer jobId) {
        try {
            log.info("启动工作流调度，jobId: {}", jobId);

            ReturnT<String> result = jobService.start(jobId);
            if (ReturnT.SUCCESS_CODE != result.getCode()) {
                log.error("启动工作流调度失败: {}", result.getMsg());
                return RequestReturnVO.fail("启动调度失败: " + result.getMsg());
            }

            log.info("工作流调度启动成功，jobId: {}", jobId);
            return RequestReturnVO.success(true);

        } catch (Exception e) {
            log.error("启动工作流调度异常", e);
            return RequestReturnVO.fail("启动工作流调度异常: " + e.getMessage());
        }
    }

    @Override
    public RequestReturnVO stopWorkflowSchedule(Integer jobId) {
        try {
            log.info("停止工作流调度，jobId: {}", jobId);

            ReturnT<String> result = jobService.stop(jobId);
            if (ReturnT.SUCCESS_CODE != result.getCode()) {
                log.error("停止工作流调度失败: {}", result.getMsg());
                return RequestReturnVO.fail("停止调度失败: " + result.getMsg());
            }

            log.info("工作流调度停止成功，jobId: {}", jobId);
            return RequestReturnVO.success(true);

        } catch (Exception e) {
            log.error("停止工作流调度异常", e);
            return RequestReturnVO.fail("停止工作流调度异常: " + e.getMessage());
        }
    }

    @Override
    public RequestReturnVO triggerWorkflowSchedule(Integer jobId, String executorParam) {
        try {
            log.info("手动触发工作流调度，jobId: {}, executorParam: {}", jobId, executorParam);

            // 使用datax-web的触发机制
            JobTriggerPoolHelper.trigger(jobId, TriggerTypeEnum.MANUAL, -1, null, executorParam);

            log.info("工作流调度触发成功，jobId: {}", jobId);
            return RequestReturnVO.success(true);

        } catch (Exception e) {
            log.error("触发工作流调度异常", e);
            return RequestReturnVO.fail("触发工作流调度异常: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public RequestReturnVO deleteWorkflowScheduleJob(Integer jobId) {
        try {
            log.info("删除工作流调度任务，jobId: {}", jobId);

            // 1. 获取工作流调度任务信息
            JobInfo workflowJob = jobInfoMapper.loadById(jobId);
            if (workflowJob == null) {
                return RequestReturnVO.fail("工作流调度任务不存在");
            }

            // 2. 解析jobJson获取工作流ID和节点任务ID列表
            Integer workflowId = null;

            try {
                Map<String, Object> jobJsonMap = JSON.parseObject(workflowJob.getJobJson(), Map.class);
                workflowId = (Integer) jobJsonMap.get("workflowId");
            } catch (Exception e) {
                log.warn("解析工作流调度任务jobJson失败: {}", e.getMessage());
            }

            // 3. 删除所有节点的调度任务
            int deletedNodeJobs = 0;
            if (workflowId != null) {
                // 获取工作流下的所有节点
                List<TaskNodeVO> taskNodes = taskNodeDao.selectTaskNodesByWorkflowId(workflowId);
                for (TaskNodeVO taskNode : taskNodes) {
                    if (taskNode.getJobId() != null) {
                        ReturnT<String> nodeResult = jobService.remove(taskNode.getJobId());
                        if (ReturnT.SUCCESS_CODE == nodeResult.getCode()) {
                            // 清除节点的jobId
                            TaskNodeVO updateNode = new TaskNodeVO();
                            updateNode.setNodeId(taskNode.getNodeId());
                            updateNode.setJobId(null);
                            taskNodeDao.updateTaskNodeData(updateNode);
                            deletedNodeJobs++;
                        } else {
                            log.warn("删除节点 {} 的调度任务失败: {}", taskNode.getNodeId(), nodeResult.getMsg());
                        }
                    }
                }
            }

            // 4. 删除工作流主调度任务
            ReturnT<String> result = jobService.remove(jobId);
            if (ReturnT.SUCCESS_CODE != result.getCode()) {
                log.error("删除工作流调度任务失败: {}", result.getMsg());
                return RequestReturnVO.fail("删除工作流调度任务失败: " + result.getMsg());
            }

            Map<String, Object> resultData = new HashMap<>();
            resultData.put("workflowJobId", jobId);
            resultData.put("deletedNodeJobs", deletedNodeJobs);
            resultData.put("workflowId", workflowId);

            log.info("工作流调度任务删除成功，jobId: {}, 删除节点任务数: {}", jobId, deletedNodeJobs);
            return RequestReturnVO.success(resultData);

        } catch (Exception e) {
            log.error("删除工作流调度任务异常", e);
            return RequestReturnVO.fail("删除工作流调度任务异常: " + e.getMessage());
        }
    }

    @Override
    public RequestReturnVO getWorkflowScheduleJob(Integer workflowId) {
        try {
            // 根据jobName模式直接查询工作流对应的调度任务
            String jobNamePattern = "WORKFLOW_" + workflowId + "_%";

            List<JobInfo> workflowJobs = jobInfoMapper.findByJobNameLike(jobNamePattern);

            if (workflowJobs == null || workflowJobs.isEmpty()) {
                return RequestReturnVO.fail("未找到工作流对应的调度任务");
            }

            // 如果有多个匹配的任务，返回第一个
            JobInfo workflowJob = workflowJobs.get(0);

            return RequestReturnVO.success(workflowJob);

        } catch (Exception e) {
            log.error("查询工作流调度任务异常", e);
            return RequestReturnVO.fail("查询工作流调度任务异常: " + e.getMessage());
        }
    }

    @Override
    public RequestReturnVO getScheduleJobById(Integer jobId) {
        try {
            JobInfo jobInfo = jobInfoMapper.loadById(jobId);
            if (jobInfo == null) {
                return RequestReturnVO.fail("调度任务不存在");
            }
            return RequestReturnVO.success(jobInfo);

        } catch (Exception e) {
            log.error("查询调度任务异常", e);
            return RequestReturnVO.fail("查询调度任务异常: " + e.getMessage());
        }
    }

    @Override
    public RequestReturnVO hasWorkflowScheduleJob(Integer workflowId) {
        try {
            RequestReturnVO result = getWorkflowScheduleJob(workflowId);
            return RequestReturnVO.success("200".equals(result.getStatus()));

        } catch (Exception e) {
            log.error("检查工作流调度任务异常", e);
            return RequestReturnVO.fail("检查工作流调度任务异常: " + e.getMessage());
        }
    }

    @Override
    public RequestReturnVO getWorkflowScheduleDetails(Integer workflowId) {
        try {
            log.info("查询工作流调度任务详情，workflowId: {}", workflowId);

            // 1. 获取工作流主调度任务
            RequestReturnVO workflowJobResult = getWorkflowScheduleJob(workflowId);
            JobInfo workflowJob = null;
            if ("200".equals(workflowJobResult.getStatus())) {
                workflowJob = (JobInfo) workflowJobResult.getResultObjVO();
            }

            // 2. 获取工作流下的所有节点
            List<TaskNodeVO> taskNodes = taskNodeDao.selectTaskNodesByWorkflowId(workflowId);

            // 3. 获取节点的调度任务信息
            List<Map<String, Object>> nodeJobs = new ArrayList<>();
            int activeJobs = 0;
            int inactiveJobs = 0;

            for (TaskNodeVO taskNode : taskNodes) {
                Map<String, Object> nodeJobInfo = new HashMap<>();
                nodeJobInfo.put("nodeId", taskNode.getNodeId());
                nodeJobInfo.put("nodeName", taskNode.getNodeName());
                nodeJobInfo.put("nodeType", taskNode.getNodeType());
                nodeJobInfo.put("childType", taskNode.getChildType());

                if (taskNode.getJobId() != null) {
                    JobInfo nodeJob = jobInfoMapper.loadById(taskNode.getJobId());
                    if (nodeJob != null) {
                        nodeJobInfo.put("jobInfo", nodeJob);
                        nodeJobInfo.put("hasJob", true);
                        if (nodeJob.getTriggerStatus() == 1) {
                            activeJobs++;
                        } else {
                            inactiveJobs++;
                        }
                    } else {
                        nodeJobInfo.put("hasJob", false);
                        nodeJobInfo.put("error", "调度任务不存在，可能已被删除");
                        inactiveJobs++;
                    }
                } else {
                    nodeJobInfo.put("hasJob", false);
                    inactiveJobs++;
                }

                nodeJobs.add(nodeJobInfo);
            }

            // 4. 构造统计信息
            Map<String, Object> summary = new HashMap<>();
            summary.put("totalNodes", taskNodes.size());
            summary.put("activeJobs", activeJobs);
            summary.put("inactiveJobs", inactiveJobs);
            summary.put("hasWorkflowJob", workflowJob != null);
            summary.put("workflowJobStatus", workflowJob != null ? workflowJob.getTriggerStatus() : null);

            // 5. 构造返回结果
            Map<String, Object> resultData = new HashMap<>();
            resultData.put("workflowJob", workflowJob);
            resultData.put("nodeJobs", nodeJobs);
            resultData.put("summary", summary);

            return RequestReturnVO.success(resultData);

        } catch (Exception e) {
            log.error("查询工作流调度任务详情异常", e);
            return RequestReturnVO.fail("查询工作流调度任务详情异常: " + e.getMessage());
        }
    }

    /**
     * 从JobInfo中提取节点ID
     * 这是一个辅助方法，用于从JobInfo的jobJson中提取nodeId
     */
    private Integer extractNodeIdFromJobInfo(JobInfo jobInfo) {
        try {
            if (jobInfo.getJobJson() != null) {
                Map<String, Object> jobJsonMap = JSON.parseObject(jobInfo.getJobJson(), Map.class);
                return (Integer) jobJsonMap.get("nodeId");
            }
        } catch (Exception e) {
            log.warn("解析JobInfo的jobJson失败: {}", e.getMessage());
        }
        return null;
    }
}
