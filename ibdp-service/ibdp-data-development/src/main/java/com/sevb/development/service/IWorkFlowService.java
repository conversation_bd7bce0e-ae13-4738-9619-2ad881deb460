package com.sevb.development.service;

import com.sevb.common.util.RequestReturnVO;
import com.sevb.development.entity.dto.InsertWorkflowDTO;
import com.sevb.development.entity.dto.UpdateWorkflowDTO;

public interface IWorkFlowService {

    /**
     * 新增工作流
     * @param dto 工作流表单
     * @return RequestReturnVO
     */
    RequestReturnVO addWorkFlowData(InsertWorkflowDTO dto);

    /**
     * 编辑工作流
     * @param dto 工作流表单
     * @return RequestReturnVO
     */
    RequestReturnVO editWorkFlowData(UpdateWorkflowDTO dto);

    /**
     * 删除工作流
     * @param workflowId 工作流ID
     * @return RequestReturnVO
     */
    RequestReturnVO removeWorkFlowData(Integer workflowId);

    /**
     * 根据ID查询工作流
     * @param workflowId 流程ID
     * @return RequestReturnVO
     */
    RequestReturnVO selectWorkFlowById(Integer workflowId);

}
