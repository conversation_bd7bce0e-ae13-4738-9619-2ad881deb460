package com.sevb.development.service;

import com.sevb.common.util.RequestReturnVO;
import com.sevb.datax.admin.entity.JobInfo;
import com.sevb.development.entity.dto.TaskNodeDTO;
import com.sevb.development.entity.vo.UpdateWorkflowEdgesVo;
import com.wugui.datatx.core.biz.model.TriggerParam;

public interface ITaskNodeService {

    /**
     * 新增任务节点
     *
     * @param taskNodeDto 任务节点表单
     * @return RequestReturnVO
     */
    RequestReturnVO addTaskNodeData(TaskNodeDTO taskNodeDto);

    /**
     * 编辑任务节点
     *
     * @param taskNodeDto 任务节点表单
     * @return RequestReturnVO
     */
    RequestReturnVO editTaskNodeData(TaskNodeDTO taskNodeDto);

    /**
     * 删除任务节点
     *
     * @param taskNodeId 任务节点ID
     * @return RequestReturnVO
     */
    RequestReturnVO removeTaskNodeData(Integer taskNodeId);

    /**
     * 查询任务节点
     *
     * @param taskNodeId 任务节点ID
     * @return RequestReturnVO
     */
    RequestReturnVO selectTaskNodeById(Integer taskNodeId);

    /**
     * 根据工作流ID查询任务节点列表
     *
     * @param workflowId 工作流ID
     * @return RequestReturnVO
     */
    RequestReturnVO getTaskNodeListByWorkflowId(Integer workflowId);

    /**
     * 查询任务节点类型树
     *
     * @return RequestReturnVO
     */
    RequestReturnVO getNodeTypeTree();

    /**
     * 更新工作流节点连线
     *
     * @param vo 更新工作流节点连线表单
     * @return RequestReturnVO
     */
    RequestReturnVO updateWorkflowEdges(UpdateWorkflowEdgesVo vo);

    /**
     * 查询任务节点执行历史
     *
     * @param nodeId 任务节点ID
     * @return RequestReturnVO
     */
    RequestReturnVO getNodeExecuteHistory(Integer nodeId, Integer pageNum, Integer pageSize);

    /**
     * 查询任务节点执行日志详情
     *
     * @param logId         日志ID
     * @param executorAddress 执行器地址
     * @param triggerTime   触发时间
     * @param fromLineNum   从第几行开始
     * @return RequestReturnVO
     */
    RequestReturnVO getTaskLogDetailCat(Integer logId, String executorAddress, Long triggerTime, Integer fromLineNum);

    /**
     * 获取任务节点任务列表
     *
     * @param pic      责任人
     * @param cdsId    任务数据源ID
     * @param pageNum  页码
     * @param pageSize 页大小
     * @return RequestReturnVO
     */
    RequestReturnVO getNodeTaskList(Integer nodeId, String pic, String cdsId, String dsType, Integer pageNum, Integer pageSize);

    /**
     * 上线任务节点
     *
     * @param jobId 任务ID
     * @return RequestReturnVO
     */
    RequestReturnVO onlineNodeTaskJob(Integer jobId);

    /**
     * 下线任务节点
     *
     * @param jobId 任务ID
     * @return com.sevb.common.util.RequestReturnVO
     */
    RequestReturnVO offlineNodeTaskJob(Integer jobId);

    /**
     * 手动触发任务
     *
     * @param jobId 任务ID
     * @return com.sevb.common.util.RequestReturnVO
     */
    RequestReturnVO triggerManualProcess(Integer jobId);
    RequestReturnVO runNodeTaskJob(TaskNodeDTO taskNodeDto);
}
