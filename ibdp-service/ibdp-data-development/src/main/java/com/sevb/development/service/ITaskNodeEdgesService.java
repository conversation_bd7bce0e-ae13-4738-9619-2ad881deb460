package com.sevb.development.service;

import com.sevb.common.util.RequestReturnVO;
import com.sevb.development.entity.vo.TaskNodeEdgesVO;
import org.apache.ibatis.annotations.Param;


public interface ITaskNodeEdgesService {

    /**
     * 新增任务节点连线
     * @param form 任务节点连线表单
     * @return RequestReturnVO
     */
    RequestReturnVO insertTaskNodeEdges(TaskNodeEdgesVO form);

    /**
     * 删除任务节点连线
     * @param edgesId 任务节点连线ID
     * @return RequestReturnVO
     */
    RequestReturnVO removeTaskNodeEdges(@Param("edgesId") String edgesId);

    /**
     * 获取任务节点连线
     * @param workflowId 工作流ID
     * @return RequestReturnVO
     */
    RequestReturnVO getTaskEdgesByWorkflowId(@Param("workflowId") Integer workflowId);

    // ==================== 从INodeDependencyService迁移的方法 ====================



    /**
     * 查询工作流的所有依赖关系
     *
     * @param workflowId 工作流ID
     * @return 依赖关系列表
     */
    RequestReturnVO getWorkflowDependencies(Integer workflowId);

    /**
     * 查询节点的上游依赖
     *
     * @param workflowId 工作流ID
     * @param nodeId 节点ID
     * @return 上游节点ID列表
     */
    RequestReturnVO getNodeUpstreamDependencies(Integer workflowId, Integer nodeId);

    /**
     * 查询节点的下游依赖
     *
     * @param workflowId 工作流ID
     * @param nodeId 节点ID
     * @return 下游节点ID列表
     */
    RequestReturnVO getNodeDownstreamDependencies(Integer workflowId, Integer nodeId);

    /**
     * 检查是否存在循环依赖
     *
     * @param workflowId 工作流ID
     * @return 检查结果
     */
    RequestReturnVO checkCircularDependency(Integer workflowId);

    /**
     * 获取工作流的拓扑排序
     *
     * @param workflowId 工作流ID
     * @return 拓扑排序结果
     */
    RequestReturnVO getTopologicalSort(Integer workflowId);

    /**
     * 查询根节点(没有依赖的节点)
     *
     * @param workflowId 工作流ID
     * @return 根节点ID列表
     */
    RequestReturnVO getRootNodes(Integer workflowId);

    /**
     * 查询叶子节点(没有被依赖的节点)
     *
     * @param workflowId 工作流ID
     * @return 叶子节点ID列表
     */
    RequestReturnVO getLeafNodes(Integer workflowId);

    /**
     * 验证依赖关系的有效性
     *
     * @param workflowId 工作流ID
     * @param sourceNodeId 源节点ID
     * @param targetNodeId 目标节点ID
     * @return 验证结果
     */
    RequestReturnVO validateDependency(Integer workflowId, Integer sourceNodeId, Integer targetNodeId);
}
