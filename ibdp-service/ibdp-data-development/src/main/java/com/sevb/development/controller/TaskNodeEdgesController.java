package com.sevb.development.controller;

import com.sevb.common.util.RequestReturnVO;
import com.sevb.development.entity.vo.TaskNodeEdgesVO;
import com.sevb.development.service.ITaskNodeEdgesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

@Slf4j
@Api(tags = "任务节点连线管理")
@RestController
@RequestMapping("/taskNodeEdges")
public class TaskNodeEdgesController {

    @Resource
    ITaskNodeEdgesService taskNodeEdgesService;

    @PostMapping("/insertTaskNodeEdges")
    @ApiOperation(value = "新增任务节点连线")
    @ApiImplicitParam(name = "form", value = "任务节点连线表单", required = true, dataType = "TaskNodeEdgesVO", dataTypeClass = TaskNodeEdgesVO.class)
    public RequestReturnVO insertTaskNodeEdges(@NotNull @RequestBody TaskNodeEdgesVO form) {
        return taskNodeEdgesService.insertTaskNodeEdges(form);
    }

    @DeleteMapping("/removeTaskNodeEdges")
    @ApiOperation(value = "删除任务节点连线")
    @ApiImplicitParam(name = "edgeId", value = "任务节点连线ID", required = true, dataType = "String", dataTypeClass = String.class)
    public RequestReturnVO removeTaskNodeEdges(@RequestParam(value = "edgeId") String edgeId) {
        return taskNodeEdgesService.removeTaskNodeEdges(edgeId);
    }

    @GetMapping("/workflow")
    @ApiOperation("查询工作流的所有连线")
    public RequestReturnVO getWorkflowEdges(
            @ApiParam("工作流ID") @RequestParam Integer workflowId) {

        return taskNodeEdgesService.getTaskEdgesByWorkflowId(workflowId);
    }

    @GetMapping("/dependency/workflow")
    @ApiOperation("查询工作流的所有依赖关系")
    public RequestReturnVO getWorkflowDependencies(
            @ApiParam("工作流ID") @RequestParam Integer workflowId) {

        return taskNodeEdgesService.getWorkflowDependencies(workflowId);
    }

    @GetMapping("/dependency/upstream")
    @ApiOperation("查询节点的上游依赖")
    public RequestReturnVO getNodeUpstreamDependencies(
            @ApiParam("工作流ID") @RequestParam Integer workflowId,
            @ApiParam("节点ID") @RequestParam Integer nodeId) {

        return taskNodeEdgesService.getNodeUpstreamDependencies(workflowId, nodeId);
    }

    @GetMapping("/dependency/downstream")
    @ApiOperation("查询节点的下游依赖")
    public RequestReturnVO getNodeDownstreamDependencies(
            @ApiParam("工作流ID") @RequestParam Integer workflowId,
            @ApiParam("节点ID") @RequestParam Integer nodeId) {

        return taskNodeEdgesService.getNodeDownstreamDependencies(workflowId, nodeId);
    }

    @GetMapping("/dependency/check-circular")
    @ApiOperation("检查是否存在循环依赖")
    public RequestReturnVO checkCircularDependency(
            @ApiParam("工作流ID") @RequestParam Integer workflowId) {

        log.info("检查循环依赖，workflowId: {}", workflowId);
        return taskNodeEdgesService.checkCircularDependency(workflowId);
    }

    @GetMapping("/dependency/topological-sort")
    @ApiOperation("获取工作流的拓扑排序")
    public RequestReturnVO getTopologicalSort(
            @ApiParam("工作流ID") @RequestParam Integer workflowId) {

        return taskNodeEdgesService.getTopologicalSort(workflowId);
    }

    @GetMapping("/dependency/root-nodes")
    @ApiOperation("查询根节点")
    public RequestReturnVO getRootNodes(
            @ApiParam("工作流ID") @RequestParam Integer workflowId) {

        return taskNodeEdgesService.getRootNodes(workflowId);
    }

    @GetMapping("/dependency/leaf-nodes")
    @ApiOperation("查询叶子节点")
    public RequestReturnVO getLeafNodes(
            @ApiParam("工作流ID") @RequestParam Integer workflowId) {

        return taskNodeEdgesService.getLeafNodes(workflowId);
    }

    @PostMapping("/dependency/validate")
    @ApiOperation("验证依赖关系的有效性")
    public RequestReturnVO validateDependency(
            @ApiParam("工作流ID") @RequestParam Integer workflowId,
            @ApiParam("源节点ID") @RequestParam Integer sourceNodeId,
            @ApiParam("目标节点ID") @RequestParam Integer targetNodeId) {

        return taskNodeEdgesService.validateDependency(workflowId, sourceNodeId, targetNodeId);
    }
}
