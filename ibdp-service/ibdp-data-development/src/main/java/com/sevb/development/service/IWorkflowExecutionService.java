package com.sevb.development.service;

import com.sevb.common.util.RequestReturnVO;

import java.util.Map;

/**
 * 工作流执行服务接口
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
public interface IWorkflowExecutionService {

    /**
     * 创建工作流实例
     *
     * @param workflowId    工作流ID
     * @param triggerUser   触发用户
     * @param triggerParams 触发参数
     * @return 工作流实例ID
     */
    RequestReturnVO triggerWorkflowExecution(Integer workflowId, String triggerUser, Map<String, Object> triggerParams);

    /**
     * 重试工作流执行
     *
     * @param instanceId  工作流实例ID
     * @param triggerUser 触发用户
     * @return 新的工作流实例ID
     */
    RequestReturnVO retryWorkflowExecution(Integer instanceId, String triggerUser);

    /**
     * 取消工作流执行
     *
     * @param instanceId 工作流实例ID
     * @param cancelUser 取消用户
     * @return 操作结果
     */
    RequestReturnVO cancelWorkflowExecution(Integer instanceId, String cancelUser);

    /**
     * 查询工作流实例详情
     *
     * @param instanceId 工作流实例ID
     * @return 工作流实例详情
     */
    RequestReturnVO getWorkflowInstanceDetail(Integer instanceId);

    /**
     * 查询任务实例列表（分页查询）
     *
     * @param instanceId      工作流实例ID(可选)
     * @param searchKeyword   搜索条件(任务名称/任务ID/实例ID)(可选)
     * @param startTime       开始时间(可选)
     * @param endTime         结束时间(可选)
     * @param executorAddress 调度主机(可选)
     * @param responsiblePerson     责任人(可选)
     * @param taskType        任务类型(子类型)(可选)
     * @param pageNum         页码
     * @param pageSize        页大小
     * @return 节点实例分页列表
     */
    RequestReturnVO getTaskNodeInstances(Integer instanceId, String searchKeyword, String startTime, String endTime,
                                         String executorAddress, String responsiblePerson, String taskType, Integer pageNum, Integer pageSize);

    /**
     * 查询工作流实例列表
     *
     * @param workflowId   工作流ID(可选)
     * @param workflowName 业务流程名称(可选)
     * @param status       状态(可选)
     * @param startDate    开始日期(可选)
     * @param endDate      结束日期(可选)
     * @param responsiblePerson  责任人(可选)
     * @param pageNum      页码
     * @param pageSize     页大小
     * @return 工作流实例列表
     */
    RequestReturnVO getWorkflowInstances(Integer workflowId, String workflowName, String status,
                                         String startDate, String endDate, String responsiblePerson, Integer pageNum, Integer pageSize);

    /**
     * 获取工作流实例执行统计信息
     *
     * @param workflowId 工作流ID
     * @return 统计信息
     */
    RequestReturnVO getWorkflowExecutionStatistics(Integer workflowId);

    /**
     * 处理节点执行完成回调
     *
     * @param nodeInstanceId 节点实例ID
     * @param success        是否成功
     * @param errorMessage   错误信息
     * @return 处理结果
     */
    RequestReturnVO handleNodeExecutionCallback(Integer nodeInstanceId, Boolean success, String errorMessage);

    /**
     * 检查并更新节点依赖状态
     *
     * @param workflowInstanceId 工作流实例ID
     * @return 更新的节点数量
     */
    RequestReturnVO checkAndUpdateNodeDependencies(Integer workflowInstanceId);

    /**
     * 获取可执行的节点实例列表
     *
     * @param workflowInstanceId 工作流实例ID
     * @return 可执行的节点实例列表
     */
    RequestReturnVO getExecutableNodeInstances(Integer workflowInstanceId);

    /**
     * 执行单个节点
     *
     * @param nodeInstanceId 节点实例ID
     * @return 执行结果
     */
    RequestReturnVO executeNode(Integer nodeInstanceId);

    /**
     * 重试失败的节点
     *
     * @param nodeInstanceId 节点实例ID
     * @param retryUser      重试用户
     * @return 重试结果
     */
    RequestReturnVO retryFailedNode(Integer nodeInstanceId, String retryUser);

    /**
     * 跳过失败的节点
     *
     * @param nodeInstanceId 节点实例ID
     * @param skipUser       跳过用户
     * @return 跳过结果
     */
    RequestReturnVO skipFailedNode(Integer nodeInstanceId, String skipUser);

    /**
     * 获取工作流实例的执行进度
     *
     * @param instanceId 工作流实例ID（可选，如果不传则获取最新实例）
     * @param workflowId 工作流ID（当instanceId为空时必传）
     * @return 执行进度信息
     */
    RequestReturnVO getWorkflowExecutionProgress(Integer instanceId, Integer workflowId);

    /**
     * 暂停工作流执行
     *
     * @param instanceId 工作流实例ID
     * @param pauseUser  暂停用户
     * @return 暂停结果
     */
    RequestReturnVO pauseWorkflowExecution(Integer instanceId, String pauseUser);

    /**
     * 恢复工作流执行
     *
     * @param instanceId 工作流实例ID
     * @param resumeUser 恢复用户
     * @return 恢复结果
     */
    RequestReturnVO resumeWorkflowExecution(Integer instanceId, String resumeUser);
}
