package com.sevb.development.service;


import com.sevb.common.util.RequestReturnVO;
import com.sevb.development.entity.vo.TaskNodeVersionVO;

public interface ITaskNodeVersionService {

    /**
     * 插入任务节点版本数据
     *
     * @param taskNodeVersionVO 任务节点版本数据
     * @return int
     */
    RequestReturnVO insertTaskNodeVersionData(TaskNodeVersionVO taskNodeVersionVO);


    /**
     * 根据任务节点ID查找历史版本
     *
     * @param nodeId 任务节点ID
     * @return List<TaskNodeVersionVO>
     */
    RequestReturnVO findListByNodeId(Integer nodeId);

    /**
     * 重置任务节点的版本
     *
     * @param nodeId        任务节点ID
     * @param activeVersion 版本号
     * @return TaskNodeVersionVO
     */
    RequestReturnVO resetActiveVersion(Integer nodeId, Integer activeVersion);

    /**
     * 根据任务节点ID和版本号查找任务节点版本
     *
     * @param nodeId 任务节点ID
     * @param version 版本号
     * @return TaskNodeVersionVO
     */
    RequestReturnVO getTaskNodeVersionByUnique(Integer nodeId, Integer version);
}
