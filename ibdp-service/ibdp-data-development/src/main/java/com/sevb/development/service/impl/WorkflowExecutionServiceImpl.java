package com.sevb.development.service.impl;

import com.alibaba.fastjson.JSON;
import com.sevb.common.util.RequestReturnVO;
import com.sevb.development.dao.IWorkflowDao;
import com.sevb.development.dao.ITaskNodeDao;
import com.sevb.development.dao.IWorkflowInstanceDao;
import com.sevb.development.dao.ITaskNodeInstanceDao;
import com.sevb.development.dao.ITaskNodeEdgesDao;
import com.sevb.development.entity.po.WorkflowPO;
import com.sevb.development.entity.po.WorkflowInstancePO;
import com.sevb.development.entity.po.TaskNodeInstancePO;
import com.sevb.development.entity.vo.TaskNodeVO;
import com.sevb.development.entity.vo.WorkflowProgressVO;
import com.sevb.development.service.IWorkflowExecutionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 工作流执行服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Slf4j
@Service
public class WorkflowExecutionServiceImpl implements IWorkflowExecutionService {

    @Resource
    private IWorkflowDao workflowDao;

    @Resource
    private ITaskNodeDao taskNodeDao;

    @Resource
    private IWorkflowInstanceDao workflowInstanceDao;

    @Resource
    private ITaskNodeInstanceDao taskNodeInstanceDao;

    @Resource
    private ITaskNodeEdgesDao taskNodeEdgesDao;

    @Override
    @Transactional
    public RequestReturnVO triggerWorkflowExecution(Integer workflowId, String triggerUser, Map<String, Object> triggerParams) {
        try {
            // 1. 验证工作流是否存在
            WorkflowPO workflow = workflowDao.selectWorkflowByWorkflowId(workflowId);
            if (workflow == null) {
                return RequestReturnVO.fail("工作流不存在");
            }

            // 2. 创建工作流实例
            WorkflowInstancePO workflowInstance = createWorkflowInstance(workflow,
                    WorkflowInstancePO.TriggerType.MANUAL.getCode(), triggerUser, triggerParams);

            // 3. 创建节点实例
            createNodeInstances(workflowInstance.getInstanceId(), workflowId);

            // 4. 初始化依赖状态
            initializeNodeDependencies(workflowInstance.getInstanceId());

            log.info("工作流实例创建成功，instanceId: {}", workflowInstance.getInstanceId());
            return RequestReturnVO.success(workflowInstance.getInstanceId());

        } catch (Exception e) {
            log.error("触发工作流执行失败", e);
            return RequestReturnVO.fail("触发工作流执行失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public RequestReturnVO retryWorkflowExecution(Integer instanceId, String triggerUser) {
        try {
            log.info("重试工作流执行，instanceId: {}, triggerUser: {}", instanceId, triggerUser);

            // 1. 获取原实例信息
            WorkflowInstancePO originalInstance = workflowInstanceDao.selectWorkflowInstanceById(instanceId);
            if (originalInstance == null) {
                return RequestReturnVO.fail("工作流实例不存在");
            }

            // 2. 检查重试次数
            if (originalInstance.getRetryCount() >= originalInstance.getMaxRetryCount()) {
                return RequestReturnVO.fail("已达到最大重试次数");
            }

            // 3. 获取工作流信息
            WorkflowPO workflow = workflowDao.selectWorkflowByWorkflowId(originalInstance.getWorkflowId());
            if (workflow == null) {
                return RequestReturnVO.fail("工作流不存在");
            }

            // 4. 创建新的工作流实例
            Map<String, Object> triggerParams = originalInstance.getTriggerParams() != null ?
                    JSON.parseObject(originalInstance.getTriggerParams(), Map.class) : new HashMap<>();

            WorkflowInstancePO newInstance = createWorkflowInstance(workflow,
                    WorkflowInstancePO.TriggerType.RETRY.getCode(), triggerUser, triggerParams);
            newInstance.setRetryCount(originalInstance.getRetryCount() + 1);

            // 5. 创建节点实例
            createNodeInstances(newInstance.getInstanceId(), workflow.getWorkflowId());

            // 6. 初始化依赖状态
            initializeNodeDependencies(newInstance.getInstanceId());

            log.info("工作流重试实例创建成功，newInstanceId: {}", newInstance.getInstanceId());
            return RequestReturnVO.success(newInstance.getInstanceId());

        } catch (Exception e) {
            log.error("重试工作流执行失败", e);
            return RequestReturnVO.fail("重试工作流执行失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public RequestReturnVO cancelWorkflowExecution(Integer instanceId, String cancelUser) {
        try {
            log.info("取消工作流执行，instanceId: {}, cancelUser: {}", instanceId, cancelUser);

            // 1. 获取工作流实例
            WorkflowInstancePO instance = workflowInstanceDao.selectWorkflowInstanceById(instanceId);
            if (instance == null) {
                return RequestReturnVO.fail("工作流实例不存在");
            }

            // 2. 检查状态是否可以取消
            if (!Arrays.asList("WAITING", "RUNNING").contains(instance.getStatus())) {
                return RequestReturnVO.fail("工作流实例状态不允许取消");
            }

            // 3. 更新工作流实例状态
            workflowInstanceDao.updateWorkflowInstanceStatus(instanceId,
                    WorkflowInstancePO.Status.CANCELLED.getCode(), "用户取消: " + cancelUser);

            // 4. 取消运行中的节点
            List<TaskNodeInstancePO> runningNodes = taskNodeInstanceDao
                    .selectTaskNodeInstancesByWorkflowInstanceIdAndStatus(instanceId, "RUNNING");

            for (TaskNodeInstancePO node : runningNodes) {
                taskNodeInstanceDao.updateTaskNodeInstanceStatus(node.getNodeInstanceId(),
                        TaskNodeInstancePO.Status.SKIPPED.getCode(), "工作流被取消");
            }

            // 5. 将等待和就绪的节点标记为跳过
            List<TaskNodeInstancePO> waitingNodes = taskNodeInstanceDao
                    .selectTaskNodeInstancesByWorkflowInstanceIdAndStatus(instanceId, "WAITING");
            List<TaskNodeInstancePO> readyNodes = taskNodeInstanceDao
                    .selectTaskNodeInstancesByWorkflowInstanceIdAndStatus(instanceId, "READY");

            List<TaskNodeInstancePO> nodesToSkip = new ArrayList<>();
            nodesToSkip.addAll(waitingNodes);
            nodesToSkip.addAll(readyNodes);

            for (TaskNodeInstancePO node : nodesToSkip) {
                taskNodeInstanceDao.updateTaskNodeInstanceStatus(node.getNodeInstanceId(),
                        TaskNodeInstancePO.Status.SKIPPED.getCode(), "工作流被取消");
            }

            log.info("工作流执行取消成功，instanceId: {}", instanceId);
            return RequestReturnVO.success(true);

        } catch (Exception e) {
            log.error("取消工作流执行失败", e);
            return RequestReturnVO.fail("取消工作流执行失败: " + e.getMessage());
        }
    }

    @Override
    public RequestReturnVO getWorkflowInstanceDetail(Integer instanceId) {
        try {
            WorkflowInstancePO instance = workflowInstanceDao.selectWorkflowInstanceById(instanceId);
            if (instance == null) {
                return RequestReturnVO.fail("工作流实例不存在");
            }
            return RequestReturnVO.success(instance);
        } catch (Exception e) {
            log.error("查询工作流实例详情失败", e);
            return RequestReturnVO.fail("查询工作流实例详情失败: " + e.getMessage());
        }
    }

    @Override
    public RequestReturnVO getTaskNodeInstances(Integer instanceId, String searchKeyword, String startTime, String endTime,
                                                String executorAddress, String responsiblePerson, String taskType, Integer pageNum, Integer pageSize) {
        try {
            // 参数处理
            Date startTimeDate = null;
            Date endTimeDate = null;

            if (startTime != null && !startTime.trim().isEmpty()) {
                try {
                    startTimeDate = java.sql.Timestamp.valueOf(startTime + " 00:00:00");
                } catch (Exception e) {
                    log.warn("开始时间格式错误: {}", startTime);
                }
            }

            if (endTime != null && !endTime.trim().isEmpty()) {
                try {
                    endTimeDate = java.sql.Timestamp.valueOf(endTime + " 23:59:59");
                } catch (Exception e) {
                    log.warn("结束时间格式错误: {}", endTime);
                }
            }

            int offset = (pageNum - 1) * pageSize;

            List<TaskNodeInstancePO> nodeInstances = taskNodeInstanceDao.selectTaskNodeInstancesWithPaging(
                    instanceId, searchKeyword, startTimeDate, endTimeDate, executorAddress, responsiblePerson, taskType, offset, pageSize);

            // 查询总数
            int totalCount = taskNodeInstanceDao.countTaskNodeInstances(
                    instanceId, searchKeyword, startTimeDate, endTimeDate, executorAddress, responsiblePerson, taskType);

            // 构建分页结果
            Map<String, Object> result = new HashMap<>();
            result.put("list", nodeInstances);
            result.put("total", totalCount);
            result.put("pageNum", pageNum);
            result.put("pageSize", pageSize);
            result.put("pages", (totalCount + pageSize - 1) / pageSize);

            return RequestReturnVO.success(result);
        } catch (Exception e) {
            log.error("查询节点实例列表失败", e);
            return RequestReturnVO.fail("查询节点实例列表失败: " + e.getMessage());
        }
    }

    /**
     * 创建工作流实例
     */
    private WorkflowInstancePO createWorkflowInstance(WorkflowPO workflow, String triggerType,
                                                      String triggerUser, Map<String, Object> triggerParams) {
        WorkflowInstancePO instance = new WorkflowInstancePO();
        instance.setWorkflowId(workflow.getWorkflowId());
        instance.setInstanceName(workflow.getWorkflowName() + "_" + System.currentTimeMillis());
        instance.setTriggerType(triggerType);
        instance.setStatus(WorkflowInstancePO.Status.WAITING.getCode());
        instance.setTriggerUser(triggerUser);
        instance.setTriggerParams(triggerParams != null ? JSON.toJSONString(triggerParams) : null);
        instance.setRetryCount(0);
        instance.setMaxRetryCount(3);

        workflowInstanceDao.insertWorkflowInstance(instance);
        return instance;
    }

    /**
     * 创建节点实例
     */
    private void createNodeInstances(Integer workflowInstanceId, Integer workflowId) {
        // 获取工作流的所有节点
        List<TaskNodeVO> nodes = taskNodeDao.selectTaskNodesByWorkflowId(workflowId);

        List<TaskNodeInstancePO> nodeInstances = new ArrayList<>();
        for (TaskNodeVO node : nodes) {
            TaskNodeInstancePO nodeInstance = new TaskNodeInstancePO();
            nodeInstance.setWorkflowInstanceId(workflowInstanceId);
            nodeInstance.setNodeId(node.getNodeId());
            nodeInstance.setNodeName(node.getNodeName());
            nodeInstance.setNodeType(node.getNodeType());
            nodeInstance.setChildType(node.getChildType()); // 从TaskNode获取子类型
            nodeInstance.setStatus(TaskNodeInstancePO.Status.WAITING.getCode());
            nodeInstance.setDependencyStatus(TaskNodeInstancePO.DependencyStatus.PENDING.getCode());
            nodeInstance.setRetryCount(0);
            nodeInstance.setMaxRetryCount(3);

            nodeInstances.add(nodeInstance);
        }

        if (!nodeInstances.isEmpty()) {
            taskNodeInstanceDao.batchInsertTaskNodeInstances(nodeInstances);
        }
    }

    /**
     * 初始化节点依赖状态
     */
    private void initializeNodeDependencies(Integer workflowInstanceId) {
        // 获取所有节点实例
        List<TaskNodeInstancePO> nodeInstances = taskNodeInstanceDao
                .selectTaskNodeInstancesByWorkflowInstanceId(workflowInstanceId);

        if (nodeInstances.isEmpty()) {
            return;
        }

        // 获取工作流ID
        WorkflowInstancePO workflowInstance = workflowInstanceDao.selectWorkflowInstanceById(workflowInstanceId);
        if (workflowInstance == null) {
            log.error("工作流实例不存在: {}", workflowInstanceId);
            return;
        }
        Integer workflowId = workflowInstance.getWorkflowId();

        // 获取所有节点信息，用于查找依赖关系
        Map<Integer, TaskNodeVO> nodeMap = new HashMap<>();
        for (TaskNodeInstancePO nodeInstance : nodeInstances) {
            TaskNodeVO node = taskNodeDao.selectTaskNodeById(nodeInstance.getNodeId());
            if (node != null) {
                nodeMap.put(node.getNodeId(), node);
            }
        }

        // 检查每个节点的依赖状态
        for (TaskNodeInstancePO nodeInstance : nodeInstances) {
            TaskNodeVO node = nodeMap.get(nodeInstance.getNodeId());
            if (node == null) {
                continue;
            }

            // 检查是否为根节点（没有上游依赖）
            boolean isRootNode = isNodeRoot(workflowId, node.getNodeId());

            if (isRootNode) {
                // 根节点的依赖状态为已满足
                taskNodeInstanceDao.updateTaskNodeInstanceDependencyStatus(
                        nodeInstance.getNodeInstanceId(),
                        TaskNodeInstancePO.DependencyStatus.SATISFIED.getCode());

                // 同时将状态更新为就绪
                taskNodeInstanceDao.updateTaskNodeInstanceStatus(
                        nodeInstance.getNodeInstanceId(),
                        TaskNodeInstancePO.Status.READY.getCode(), null);
            }
        }
    }

    /**
     * 检查节点是否为根节点（没有上游依赖）
     */
    private boolean isNodeRoot(Integer workflowId, Integer nodeId) {
        try {
            // 检查task_edges表中的依赖关系
            List<Integer> sourceNodeIds = taskNodeEdgesDao.selectSourceNodeIdsByTargetNodeId(workflowId, nodeId);
            return sourceNodeIds.isEmpty();

        } catch (Exception e) {
            log.error("检查节点是否为根节点异常，nodeId: {}", nodeId, e);
            return false;
        }
    }

    /**
     * 获取工作流实例列表
     */
    @Override
    public RequestReturnVO getWorkflowInstances(Integer workflowId, String workflowName, String status,
                                                String startDate, String endDate, String responsiblePerson, Integer pageNum, Integer pageSize) {
        try {
            // 参数处理
            Date startDateTime = null;
            Date endDateTime = null;

            if (startDate != null && !startDate.trim().isEmpty()) {
                try {
                    startDateTime = java.sql.Date.valueOf(startDate);
                } catch (Exception e) {
                    log.warn("开始日期格式错误: {}", startDate);
                }
            }

            if (endDate != null && !endDate.trim().isEmpty()) {
                try {
                    // 结束日期设置为当天的23:59:59
                    endDateTime = java.sql.Timestamp.valueOf(endDate + " 23:59:59");
                } catch (Exception e) {
                    log.warn("结束日期格式错误: {}", endDate);
                }
            }

            int offset = (pageNum - 1) * pageSize;

            List<WorkflowInstancePO> instances = workflowInstanceDao.selectWorkflowInstancesWithPaging(
                    workflowId, workflowName, status, startDateTime, endDateTime,
                    responsiblePerson, offset, pageSize);

            // 查询总数
            int totalCount = workflowInstanceDao.countWorkflowInstances(
                    workflowId, workflowName, status, startDateTime, endDateTime,
                    responsiblePerson);

            // 构建分页结果
            Map<String, Object> result = new HashMap<>();
            result.put("list", instances);
            result.put("total", totalCount);
            result.put("pageNum", pageNum);
            result.put("pageSize", pageSize);
            result.put("pages", (totalCount + pageSize - 1) / pageSize);

            return RequestReturnVO.success(result);
        } catch (Exception e) {
            log.error("查询工作流实例列表失败", e);
            return RequestReturnVO.fail("查询工作流实例列表失败: " + e.getMessage());
        }
    }

    @Override
    public RequestReturnVO getWorkflowExecutionStatistics(Integer workflowId) {
        try {
            List<WorkflowInstancePO> allInstances = workflowInstanceDao.selectWorkflowInstancesByWorkflowId(workflowId);

            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalCount", allInstances.size());
            statistics.put("successCount", allInstances.stream().mapToInt(i -> "SUCCESS".equals(i.getStatus()) ? 1 : 0).sum());
            statistics.put("failedCount", allInstances.stream().mapToInt(i -> "FAILED".equals(i.getStatus()) ? 1 : 0).sum());
            statistics.put("runningCount", allInstances.stream().mapToInt(i -> "RUNNING".equals(i.getStatus()) ? 1 : 0).sum());
            statistics.put("cancelledCount", allInstances.stream().mapToInt(i -> "CANCELLED".equals(i.getStatus()) ? 1 : 0).sum());

            return RequestReturnVO.success(statistics);
        } catch (Exception e) {
            log.error("获取工作流执行统计信息失败", e);
            return RequestReturnVO.fail("获取工作流执行统计信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public RequestReturnVO handleNodeExecutionCallback(Integer nodeInstanceId, Boolean success, String errorMessage) {
        try {
            log.info("处理节点执行回调，nodeInstanceId: {}, success: {}", nodeInstanceId, success);

            TaskNodeInstancePO nodeInstance = taskNodeInstanceDao.selectTaskNodeInstanceById(nodeInstanceId);
            if (nodeInstance == null) {
                return RequestReturnVO.fail("节点实例不存在");
            }

            Date endTime = new Date();
            long durationMs = nodeInstance.getStartTime() != null ?
                    endTime.getTime() - nodeInstance.getStartTime().getTime() : 0;

            if (success) {
                // 更新节点状态为成功
                taskNodeInstanceDao.updateTaskNodeInstanceStatus(nodeInstanceId,
                        TaskNodeInstancePO.Status.SUCCESS.getCode(), null);
                taskNodeInstanceDao.updateTaskNodeInstanceEndTime(nodeInstanceId, endTime, durationMs);

                // 检查并更新下游节点的依赖状态
                checkAndUpdateNodeDependencies(nodeInstance.getWorkflowInstanceId());

                // 检查工作流是否完成
                checkWorkflowCompletion(nodeInstance.getWorkflowInstanceId());

            } else {
                // 更新节点状态为失败
                taskNodeInstanceDao.updateTaskNodeInstanceStatus(nodeInstanceId,
                        TaskNodeInstancePO.Status.FAILED.getCode(), errorMessage);
                taskNodeInstanceDao.updateTaskNodeInstanceEndTime(nodeInstanceId, endTime, durationMs);

                // 检查是否需要重试
                if (nodeInstance.getRetryCount() < nodeInstance.getMaxRetryCount()) {
                    log.info("节点执行失败，可以重试，nodeInstanceId: {}", nodeInstanceId);
                } else {
                    // 达到最大重试次数，标记工作流失败
                    markWorkflowAsFailed(nodeInstance.getWorkflowInstanceId(), "节点执行失败: " + errorMessage);
                }
            }

            return RequestReturnVO.success(true);

        } catch (Exception e) {
            log.error("处理节点执行回调失败", e);
            return RequestReturnVO.fail("处理节点执行回调失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public RequestReturnVO checkAndUpdateNodeDependencies(Integer workflowInstanceId) {
        try {
            List<TaskNodeInstancePO> nodeInstances = taskNodeInstanceDao
                    .selectTaskNodeInstancesByWorkflowInstanceId(workflowInstanceId);

            if (nodeInstances.isEmpty()) {
                return RequestReturnVO.success(0);
            }

            // 获取工作流ID
            Integer workflowId = nodeInstances.get(0).getWorkflowInstanceId();
            WorkflowInstancePO workflowInstance = workflowInstanceDao.selectWorkflowInstanceById(workflowInstanceId);
            if (workflowInstance != null) {
                workflowId = workflowInstance.getWorkflowId();
            }

            // 构建节点实例映射
            Map<Integer, TaskNodeInstancePO> nodeInstanceMap = nodeInstances.stream()
                    .collect(Collectors.toMap(TaskNodeInstancePO::getNodeId, n -> n));

            int updatedCount = 0;

            for (TaskNodeInstancePO nodeInstance : nodeInstances) {
                // 跳过已经完成或失败的节点
                if (Arrays.asList("SUCCESS", "FAILED", "SKIPPED", "RUNNING").contains(nodeInstance.getStatus())) {
                    continue;
                }

                boolean dependenciesSatisfied = checkNodeDependenciesSatisfied(
                        workflowId, nodeInstance.getNodeId(), nodeInstanceMap);

                // 更新依赖状态
                String newDependencyStatus = dependenciesSatisfied ?
                        TaskNodeInstancePO.DependencyStatus.SATISFIED.getCode() :
                        TaskNodeInstancePO.DependencyStatus.PENDING.getCode();

                if (!newDependencyStatus.equals(nodeInstance.getDependencyStatus())) {
                    taskNodeInstanceDao.updateTaskNodeInstanceDependencyStatus(
                            nodeInstance.getNodeInstanceId(), newDependencyStatus);

                    // 如果依赖满足，将节点状态更新为就绪
                    if (dependenciesSatisfied && "WAITING".equals(nodeInstance.getStatus())) {
                        taskNodeInstanceDao.updateTaskNodeInstanceStatus(
                                nodeInstance.getNodeInstanceId(),
                                TaskNodeInstancePO.Status.READY.getCode(), null);
                    }

                    updatedCount++;
                }
            }

            return RequestReturnVO.success(updatedCount);

        } catch (Exception e) {
            log.error("检查并更新节点依赖状态失败", e);
            return RequestReturnVO.fail("检查并更新节点依赖状态失败: " + e.getMessage());
        }
    }

    @Override
    public RequestReturnVO getExecutableNodeInstances(Integer workflowInstanceId) {
        try {
            List<TaskNodeInstancePO> readyNodes = taskNodeInstanceDao.selectReadyTaskNodeInstances(workflowInstanceId);
            return RequestReturnVO.success(readyNodes);
        } catch (Exception e) {
            log.error("获取可执行节点实例失败", e);
            return RequestReturnVO.fail("获取可执行节点实例失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public RequestReturnVO executeNode(Integer nodeInstanceId) {
        try {
            log.info("执行节点，nodeInstanceId: {}", nodeInstanceId);

            TaskNodeInstancePO nodeInstance = taskNodeInstanceDao.selectTaskNodeInstanceById(nodeInstanceId);
            if (nodeInstance == null) {
                return RequestReturnVO.fail("节点实例不存在");
            }

            if (!"READY".equals(nodeInstance.getStatus())) {
                return RequestReturnVO.fail("节点状态不允许执行");
            }

            // 更新节点状态为运行中
            Date startTime = new Date();
            taskNodeInstanceDao.updateTaskNodeInstanceStartTime(nodeInstanceId, startTime);

            return RequestReturnVO.success(true);

        } catch (Exception e) {
            log.error("执行节点失败", e);
            return RequestReturnVO.fail("执行节点失败: " + e.getMessage());
        }
    }

    @Override
    public RequestReturnVO retryFailedNode(Integer nodeInstanceId, String retryUser) {
        return RequestReturnVO.success(true);
    }

    @Override
    public RequestReturnVO skipFailedNode(Integer nodeInstanceId, String skipUser) {
        return RequestReturnVO.success(true);
    }

    @Override
    public RequestReturnVO getWorkflowExecutionProgress(Integer instanceId, Integer workflowId) {
        try {
            log.info("获取工作流执行进度，instanceId: {}, workflowId: {}", instanceId, workflowId);

            WorkflowInstancePO workflowInstance = null;

            // 1. 获取工作流实例信息
            if (instanceId != null) {
                // 如果传了instanceId，直接查询该实例
                workflowInstance = workflowInstanceDao.selectWorkflowInstanceById(instanceId);
                if (workflowInstance == null) {
                    return RequestReturnVO.fail("工作流实例不存在");
                }
            } else if (workflowId != null) {
                // 如果没传instanceId但传了workflowId，查询最新实例
                workflowInstance = workflowInstanceDao.selectLatestWorkflowInstanceByWorkflowId(workflowId);
                if (workflowInstance == null) {
                    return RequestReturnVO.fail("该工作流暂无执行实例");
                }
            } else {
                return RequestReturnVO.fail("instanceId和workflowId不能同时为空");
            }

            // 2. 获取工作流基本信息
            WorkflowPO workflow = workflowDao.selectWorkflowByWorkflowId(workflowInstance.getWorkflowId());
            if (workflow == null) {
                return RequestReturnVO.fail("工作流不存在");
            }

            // 3. 获取所有节点实例
            List<TaskNodeInstancePO> nodeInstances = taskNodeInstanceDao
                    .selectTaskNodeInstancesByWorkflowInstanceId(workflowInstance.getInstanceId());

            // 4. 构建进度VO
            WorkflowProgressVO progressVO = buildWorkflowExecutionProgress(
                    workflowInstance, workflow, nodeInstances);

            return RequestReturnVO.success(progressVO);

        } catch (Exception e) {
            log.error("获取工作流执行进度失败，instanceId: {}, workflowId: {}", instanceId, workflowId, e);
            return RequestReturnVO.fail("获取工作流执行进度失败: " + e.getMessage());
        }
    }

    @Override
    public RequestReturnVO pauseWorkflowExecution(Integer instanceId, String pauseUser) {
        return RequestReturnVO.success(true);
    }

    @Override
    public RequestReturnVO resumeWorkflowExecution(Integer instanceId, String resumeUser) {
        return RequestReturnVO.success(true);
    }

    /**
     * 检查节点的所有依赖是否已满足（强依赖）
     */
    private boolean checkNodeDependenciesSatisfied(Integer workflowId, Integer nodeId,
                                                   Map<Integer, TaskNodeInstancePO> nodeInstanceMap) {
        try {
            // 检查task_edges表中的依赖关系
            List<Integer> sourceNodeIds = taskNodeEdgesDao.selectSourceNodeIdsByTargetNodeId(workflowId, nodeId);
            for (Integer sourceNodeId : sourceNodeIds) {
                TaskNodeInstancePO sourceInstance = nodeInstanceMap.get(sourceNodeId);
                if (sourceInstance == null || !"SUCCESS".equals(sourceInstance.getStatus())) {
                    return false; // 强依赖：必须所有上游节点都成功
                }
            }

            return true;

        } catch (Exception e) {
            log.error("检查节点依赖状态异常，nodeId: {}", nodeId, e);
            return false;
        }
    }

    /**
     * 检查工作流是否完成
     */
    private void checkWorkflowCompletion(Integer workflowInstanceId) {
        try {
            List<TaskNodeInstancePO> nodeInstances = taskNodeInstanceDao
                    .selectTaskNodeInstancesByWorkflowInstanceId(workflowInstanceId);

            boolean allCompleted = nodeInstances.stream()
                    .allMatch(node -> Arrays.asList("SUCCESS", "FAILED", "SKIPPED").contains(node.getStatus()));

            if (allCompleted) {
                boolean allSuccess = nodeInstances.stream()
                        .allMatch(node -> Arrays.asList("SUCCESS", "SKIPPED").contains(node.getStatus()));

                String finalStatus = allSuccess ?
                        WorkflowInstancePO.Status.SUCCESS.getCode() :
                        WorkflowInstancePO.Status.FAILED.getCode();

                Date endTime = new Date();
                WorkflowInstancePO instance = workflowInstanceDao.selectWorkflowInstanceById(workflowInstanceId);
                long durationMs = instance.getStartTime() != null ?
                        endTime.getTime() - instance.getStartTime().getTime() : 0;

                workflowInstanceDao.updateWorkflowInstanceStatus(workflowInstanceId, finalStatus, null);
                workflowInstanceDao.updateWorkflowInstanceEndTime(workflowInstanceId, endTime, durationMs);

                log.info("工作流执行完成，instanceId: {}, status: {}", workflowInstanceId, finalStatus);
            }
        } catch (Exception e) {
            log.error("检查工作流完成状态异常", e);
        }
    }

    /**
     * 标记工作流为失败
     */
    private void markWorkflowAsFailed(Integer workflowInstanceId, String errorMessage) {
        try {
            Date endTime = new Date();
            WorkflowInstancePO instance = workflowInstanceDao.selectWorkflowInstanceById(workflowInstanceId);
            long durationMs = instance.getStartTime() != null ?
                    endTime.getTime() - instance.getStartTime().getTime() : 0;

            workflowInstanceDao.updateWorkflowInstanceStatus(workflowInstanceId,
                    WorkflowInstancePO.Status.FAILED.getCode(), errorMessage);
            workflowInstanceDao.updateWorkflowInstanceEndTime(workflowInstanceId, endTime, durationMs);

            log.info("工作流标记为失败，instanceId: {}, error: {}", workflowInstanceId, errorMessage);
        } catch (Exception e) {
            log.error("标记工作流失败异常", e);
        }
    }

    /**
     * 构建工作流执行进度VO
     */
    private WorkflowProgressVO buildWorkflowExecutionProgress(
            WorkflowInstancePO workflowInstance, WorkflowPO workflow, List<TaskNodeInstancePO> nodeInstances) {

        WorkflowProgressVO progressVO = new WorkflowProgressVO();

        // 设置工作流实例基本信息
        progressVO.setInstanceId(workflowInstance.getInstanceId());
        progressVO.setWorkflowId(workflowInstance.getWorkflowId());
        progressVO.setWorkflowName(workflow.getWorkflowName());
        progressVO.setInstanceName(workflowInstance.getInstanceName());
        progressVO.setStatus(workflowInstance.getStatus());
        progressVO.setStatusDesc(WorkflowProgressVO.getStatusDesc(workflowInstance.getStatus()));
        progressVO.setTriggerType(workflowInstance.getTriggerType());
        progressVO.setTriggerUser(workflowInstance.getTriggerUser());
        progressVO.setStartTime(workflowInstance.getStartTime());
        progressVO.setEndTime(workflowInstance.getEndTime());
        progressVO.setDurationMs(workflowInstance.getDurationMs());
        progressVO.setDurationFormatted(WorkflowProgressVO.formatDuration(workflowInstance.getDurationMs()));
        progressVO.setErrorMessage(workflowInstance.getErrorMessage());
        progressVO.setRetryCount(workflowInstance.getRetryCount());
        progressVO.setMaxRetryCount(workflowInstance.getMaxRetryCount());

        // 统计节点状态
        int totalNodes = nodeInstances.size();
        int waitingNodes = 0;
        int readyNodes = 0;
        int runningNodes = 0;
        int successNodes = 0;
        int failedNodes = 0;
        int skippedNodes = 0;

        for (TaskNodeInstancePO nodeInstance : nodeInstances) {
            String status = nodeInstance.getStatus();
            switch (status) {
                case "WAITING":
                    waitingNodes++;
                    break;
                case "READY":
                    readyNodes++;
                    break;
                case "RUNNING":
                    runningNodes++;
                    break;
                case "SUCCESS":
                    successNodes++;
                    break;
                case "FAILED":
                    failedNodes++;
                    break;
                case "SKIPPED":
                    skippedNodes++;
                    break;
            }
        }

        progressVO.setTotalNodes(totalNodes);
        progressVO.setWaitingNodes(waitingNodes);
        progressVO.setReadyNodes(readyNodes);
        progressVO.setRunningNodes(runningNodes);
        progressVO.setSuccessNodes(successNodes);
        progressVO.setFailedNodes(failedNodes);
        progressVO.setSkippedNodes(skippedNodes);

        // 计算完成进度百分比
        int completedNodes = successNodes + failedNodes + skippedNodes;
        double progressPercentage = totalNodes > 0 ? (double) completedNodes / totalNodes * 100 : 0.0;
        progressVO.setProgressPercentage(Math.round(progressPercentage * 100.0) / 100.0);

        // 构建节点详情列表
        List<WorkflowProgressVO.NodeExecutionDetailVO> nodeDetails = new ArrayList<>();
        for (TaskNodeInstancePO nodeInstance : nodeInstances) {
            WorkflowProgressVO.NodeExecutionDetailVO detailVO = buildNodeExecutionDetail(nodeInstance);
            nodeDetails.add(detailVO);
        }
        progressVO.setNodeDetails(nodeDetails);

        return progressVO;
    }

    /**
     * 构建节点执行详情VO
     */
    private WorkflowProgressVO.NodeExecutionDetailVO buildNodeExecutionDetail(TaskNodeInstancePO nodeInstance) {
        WorkflowProgressVO.NodeExecutionDetailVO detailVO =
                new WorkflowProgressVO.NodeExecutionDetailVO();

        detailVO.setNodeInstanceId(nodeInstance.getNodeInstanceId());
        detailVO.setNodeId(nodeInstance.getNodeId());
        detailVO.setNodeName(nodeInstance.getNodeName());
        detailVO.setNodeType(nodeInstance.getNodeType());
        detailVO.setChildType(nodeInstance.getChildType());
        detailVO.setStatus(nodeInstance.getStatus());
        detailVO.setStatusDesc(WorkflowProgressVO.getStatusDesc(nodeInstance.getStatus()));
        detailVO.setStartTime(nodeInstance.getStartTime());
        detailVO.setEndTime(nodeInstance.getEndTime());
        detailVO.setDurationMs(nodeInstance.getDurationMs());
        detailVO.setDurationFormatted(WorkflowProgressVO.formatDuration(nodeInstance.getDurationMs()));
        detailVO.setExecutorAddress(nodeInstance.getExecutorAddress());
        detailVO.setExecutorHandler(nodeInstance.getExecutorHandler());
        detailVO.setErrorMessage(nodeInstance.getErrorMessage());
        detailVO.setRetryCount(nodeInstance.getRetryCount());
        detailVO.setMaxRetryCount(nodeInstance.getMaxRetryCount());
        detailVO.setDependencyStatus(nodeInstance.getDependencyStatus());
        detailVO.setJobLogId(nodeInstance.getJobLogId());

        return detailVO;
    }
}
