package com.sevb.development.service.impl;

import com.sevb.common.util.BeanUtils;
import com.sevb.common.util.RequestReturnVO;
import com.sevb.development.dao.IDirectoryDao;
import com.sevb.development.dao.IWorkflowDao;
import com.sevb.development.entity.dto.InsertDirectoryDTO;
import com.sevb.development.entity.dto.UpdateDirectoryDto;
import com.sevb.development.entity.po.DirectoryPO;
import com.sevb.development.entity.po.TaskNodePO;
import com.sevb.development.entity.po.WorkflowPO;
import com.sevb.development.entity.vo.DirectoryVO;
import com.sevb.development.service.IDirectoryService;
import com.sevb.project.dao.IProjectDao;
import com.sevb.project.entity.vo.ProjectVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class DirectoryServiceImpl implements IDirectoryService {

    @Resource
    private IDirectoryDao directoryDao;

    @Resource
    private IWorkflowDao workflowDao;

    @Resource
    private IProjectDao projectDao;

    @Override
    public RequestReturnVO addDirectoryData(InsertDirectoryDTO dto) {
        // 判断父ID是否存在
        if (dto.getParentDirectoryId() != null && dto.getParentDirectoryId() != 0) {
            DirectoryPO directoryData = directoryDao.selectDirectoryById(dto.getParentDirectoryId());
            if (directoryData == null) {
                return RequestReturnVO.fail("父目录ID不存在");
            }
        } else {
            dto.setParentDirectoryId(0);
        }
        //判断项目ID是否存在
        if (dto.getProjectId() != null) {
            ProjectVO projectData = projectDao.getProjectDataById(dto.getProjectId());
            if (projectData == null) {
                return RequestReturnVO.fail("项目ID不存在");
            }
        } else {
            return RequestReturnVO.fail("项目ID不能为空");
        }
        DirectoryVO vo = new DirectoryVO();
        BeanUtils.copyProperties(dto, vo);
        int result = directoryDao.insertDirectoryData(vo);
        return result > 0 ? RequestReturnVO.success(vo.getDirectoryId()) : RequestReturnVO.fail("新增失败");
    }

    @Override
    public RequestReturnVO editDirectoryData(UpdateDirectoryDto dto) {
        // 判断父ID是否存在
        if (dto.getParentDirectoryId() != null && dto.getParentDirectoryId() != 0) {
            DirectoryPO directoryData = directoryDao.selectDirectoryById(dto.getParentDirectoryId());
            if (directoryData == null) {
                return RequestReturnVO.fail("父目录ID不存在");
            }
        }
        //判断项目ID是否存在
        if (dto.getProjectId() != null) {
            ProjectVO projectData = projectDao.getProjectDataById(dto.getProjectId());
            if (projectData == null) {
                return RequestReturnVO.fail("项目ID不存在");
            }
        }
        DirectoryVO vo = new DirectoryVO();
        BeanUtils.copyProperties(dto, vo);
        int result = directoryDao.updateDirectoryData(vo);
        return result > 0 ? RequestReturnVO.success() : RequestReturnVO.fail("修改失败");
    }

    @Override
    public RequestReturnVO removeDirectoryData(Integer directoryId) {
        // 检查是否有工作流
        if (!workflowDao.selectWorkflowByDirectoryIds(Collections.singletonList(directoryId)).isEmpty()) {
            return RequestReturnVO.fail("该目录下有工作流，不能删除");
        }
        // 检查是否有子节点
        if (directoryDao.selectDirectoryByParentDirectoryId(directoryId) != null) {
            return RequestReturnVO.fail("该目录下有子节点，不能删除");
        }
        int result = directoryDao.deleteDirectoryData(directoryId);
        return result > 0 ? RequestReturnVO.success() : RequestReturnVO.fail("删除失败");
    }

    @Override
    public RequestReturnVO selectDirectoryById(Integer directoryId) {
        return RequestReturnVO.success(directoryDao.selectDirectoryById(directoryId));
    }

    @Override
    public RequestReturnVO selectDirectoryByProjectId(String projectId) {
        // 1. 查询目录数据
        List<DirectoryPO> directories = directoryDao.selectDirectoryByProjectId(projectId);
        if (directories == null || directories.isEmpty()) {
            return RequestReturnVO.success();
        }

        // 2. 初始化目录基础属性
        directories.forEach(directory -> {
            directory.setType("directory");
            directory.setCount(0); // 初始化为0
        });

        // 3. 获取目录ID列表用于批量查询
        List<Integer> directoryIds = directories.stream()
                .map(DirectoryPO::getDirectoryId)
                .collect(Collectors.toList());

        // 4. 查询关联的工作流数据
        List<WorkflowPO> workflows = workflowDao.selectWorkflowByDirectoryIds(directoryIds);

        if (!workflows.isEmpty()) {
            // 5. 初始化工作流基础属性
            workflows.forEach(workflow -> {
                workflow.setType("workflow");
                workflow.setCount(0); // 初始化为0
            });

            // 6. 批量查询任务节点数据
            List<Integer> workflowIds = workflows.stream()
                    .map(WorkflowPO::getWorkflowId)
                    .collect(Collectors.toList());
            List<TaskNodePO> taskNodes = workflowDao.selectTaskNodesByWorkflowIds(workflowIds);

            // 7. 处理节点数据并分组
            Map<Integer, List<TaskNodePO>> taskNodeMap = taskNodes.stream()
                    .peek(node -> node.setType("node"))
                    .collect(Collectors.groupingBy(TaskNodePO::getWorkflowId));

            // 8. 更新工作流的节点信息和count
            workflows.forEach(workflow -> {
                List<TaskNodePO> nodes = taskNodeMap.getOrDefault(workflow.getWorkflowId(), Collections.emptyList());
                workflow.setTaskNodes(nodes);
                workflow.setCount(nodes.size()); // 工作流count=节点数量
            });

            // 9. 按目录ID分组工作流
            Map<Integer, List<WorkflowPO>> workflowMap = workflows.stream()
                    .collect(Collectors.groupingBy(WorkflowPO::getDirectoryId));

            // 10. 更新目录的工作流信息和count
            directories.forEach(directory -> {
                List<WorkflowPO> dirWorkflows = workflowMap.getOrDefault(directory.getDirectoryId(), Collections.emptyList());
                directory.setWorkflows(dirWorkflows);
                // 目录count=工作流数量（不包括节点数）
                directory.setCount(dirWorkflows.size());
            });
        }

        // 11. 构建树形结构并递归计算count
        List<DirectoryPO> result = buildDirectoryTreeWithCount(directories);
        return RequestReturnVO.success(result);
    }

    /**
     * 构建树形结构并递归计算count（父目录count包含子目录count）
     */
    private List<DirectoryPO> buildDirectoryTreeWithCount(List<DirectoryPO> directories) {
        Map<Integer, DirectoryPO> directoryMap = new HashMap<>(directories.size());
        List<DirectoryPO> rootDirectories = new ArrayList<>();

        // 第一遍：建立映射和收集根目录
        for (DirectoryPO directory : directories) {
            directoryMap.put(directory.getDirectoryId(), directory);
            if (directory.getParentDirectoryId() == 0) {
                rootDirectories.add(directory);
            }
        }

        // 第二遍：建立父子关系
        for (DirectoryPO directory : directories) {
            if (directory.getParentDirectoryId() != 0) {
                DirectoryPO parent = directoryMap.get(directory.getParentDirectoryId());
                if (parent != null) {
                    if (parent.getChildren() == null) {
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(directory);
                }
            }
        }

        // 第三遍：递归计算count（父目录包含子目录的count）
        for (DirectoryPO directory : directories) {
            if (directory.getChildren() != null && !directory.getChildren().isEmpty()) {
                int childrenCountSum = directory.getChildren().stream()
                        .mapToInt(DirectoryPO::getCount)
                        .sum();
                directory.setCount(directory.getCount() + childrenCountSum);
            }
        }

        return rootDirectories;
    }
}
