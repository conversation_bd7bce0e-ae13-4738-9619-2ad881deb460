package com.sevb.development.controller;

import com.sevb.common.util.RequestReturnVO;
import com.sevb.development.service.IWorkflowScheduleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 工作流调度控制器
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Slf4j
@RestController
@RequestMapping("/workflow/schedule")
@Api(tags = "工作流调度管理")
public class WorkflowScheduleController {

    @Resource
    private IWorkflowScheduleService workflowScheduleService;

    @PostMapping("/create")
    @ApiOperation(value = "为工作流创建或更新调度任务",
            notes = "为工作流及其下的所有节点创建或更新调度任务。如果工作流已存在调度任务则进行更新，否则创建新任务。工作流本身创建一个ds_type为'workflow'的主任务，每个节点创建独立的调度任务。")
    public RequestReturnVO createWorkflowScheduleJob(
            @ApiParam("工作流ID") @RequestParam Integer workflowId) {
        return workflowScheduleService.createWorkflowScheduleJob(workflowId);
    }

    @PostMapping("/start")
    @ApiOperation("启动工作流调度")
    public RequestReturnVO startWorkflowSchedule(
            @ApiParam("任务ID") @RequestParam Integer jobId) {

        log.info("启动工作流调度，jobId: {}", jobId);
        return workflowScheduleService.startWorkflowSchedule(jobId);
    }

    @PostMapping("/stop")
    @ApiOperation("停止工作流调度")
    public RequestReturnVO stopWorkflowSchedule(
            @ApiParam("任务ID") @RequestParam Integer jobId) {

        log.info("停止工作流调度，jobId: {}", jobId);
        return workflowScheduleService.stopWorkflowSchedule(jobId);
    }

    @PostMapping("/trigger")
    @ApiOperation("手动触发工作流调度")
    public RequestReturnVO triggerWorkflowSchedule(
            @ApiParam("任务ID") @RequestParam Integer jobId,
            @ApiParam("执行参数") @RequestParam(required = false) String executorParam) {

        log.info("手动触发工作流调度，jobId: {}, executorParam: {}", jobId, executorParam);
        return workflowScheduleService.triggerWorkflowSchedule(jobId, executorParam);
    }

    @DeleteMapping("/delete")
    @ApiOperation(value = "删除工作流调度任务",
            notes = "删除工作流主调度任务及其下所有节点的调度任务，并清理节点表中的job_id字段。")
    public RequestReturnVO deleteWorkflowScheduleJob(
            @ApiParam("工作流主任务ID") @RequestParam Integer jobId) {

        log.info("删除工作流调度任务，jobId: {}", jobId);
        return workflowScheduleService.deleteWorkflowScheduleJob(jobId);
    }

    @GetMapping("/workflow")
    @ApiOperation("根据工作流ID查询调度任务")
    public RequestReturnVO getWorkflowScheduleJob(
            @ApiParam("工作流ID") @RequestParam Integer workflowId) {

        return workflowScheduleService.getWorkflowScheduleJob(workflowId);
    }

    @GetMapping("/job")
    @ApiOperation("根据任务ID查询调度任务")
    public RequestReturnVO getScheduleJobById(
            @ApiParam("任务ID") @RequestParam Integer jobId) {

        return workflowScheduleService.getScheduleJobById(jobId);
    }

    @GetMapping("/exists")
    @ApiOperation("检查工作流是否已有调度任务")
    public RequestReturnVO hasWorkflowScheduleJob(
            @ApiParam("工作流ID") @RequestParam Integer workflowId) {

        return workflowScheduleService.hasWorkflowScheduleJob(workflowId);
    }

    @GetMapping("/details")
    @ApiOperation(value = "查询工作流调度任务详情",
            notes = "返回工作流主任务和所有节点任务的详细信息")
    public RequestReturnVO getWorkflowScheduleDetails(
            @ApiParam("工作流ID") @RequestParam Integer workflowId) {

        return workflowScheduleService.getWorkflowScheduleDetails(workflowId);
    }
}
