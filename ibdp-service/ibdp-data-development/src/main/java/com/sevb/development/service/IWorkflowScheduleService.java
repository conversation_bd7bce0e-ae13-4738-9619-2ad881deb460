package com.sevb.development.service;

import com.sevb.common.util.RequestReturnVO;

import java.util.Map;

/**
 * 工作流调度服务接口
 * 负责将工作流集成到datax-web调度系统中
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
public interface IWorkflowScheduleService {

    /**
     * 为工作流创建或更新调度任务
     * <p>
     * 功能说明：
     * 1. 为工作流下的每个节点创建或更新独立的调度任务（基于节点配置）
     * 2. 为工作流本身创建或更新一个主调度任务（ds_type为"workflow"）
     * 3. 更新节点表中的job_id字段
     * 4. 如果工作流已存在调度任务则进行更新，否则创建新任务
     *
     * @param workflowId    工作流ID
     * @return 返回数据包含：workflowJobId(工作流主任务ID), nodeJobIds(节点任务ID列表), totalNodes(总节点数), processedNodes(成功处理的节点数), isUpdate(是否为更新操作)
     */
    RequestReturnVO createWorkflowScheduleJob(Integer workflowId);

    /**
     * 更新工作流调度任务
     *
     * @param jobId         任务ID
     * @param jobCron       CRON表达式
     * @param jobDesc       任务描述
     * @param triggerParams 触发参数
     * @return 更新结果
     */
    RequestReturnVO updateWorkflowScheduleJob(Integer jobId, String jobCron, String jobDesc,
                                              Map<String, Object> triggerParams);

    /**
     * 启动工作流调度
     *
     * @param jobId 任务ID
     * @return 启动结果
     */
    RequestReturnVO startWorkflowSchedule(Integer jobId);

    /**
     * 停止工作流调度
     *
     * @param jobId 任务ID
     * @return 停止结果
     */
    RequestReturnVO stopWorkflowSchedule(Integer jobId);

    /**
     * 手动触发工作流执行
     *
     * @param jobId         任务ID
     * @param executorParam 执行参数
     * @return 触发结果
     */
    RequestReturnVO triggerWorkflowSchedule(Integer jobId, String executorParam);

    /**
     * 删除工作流调度任务
     * 功能说明：
     * 1. 删除工作流主调度任务
     * 2. 删除工作流下所有节点的调度任务
     * 3. 清理节点表中的job_id字段
     *
     * @param jobId 工作流主任务ID
     * @return 返回数据包含：workflowJobId(工作流主任务ID), deletedNodeJobs(删除的节点任务数), workflowId(工作流ID)
     */
    RequestReturnVO deleteWorkflowScheduleJob(Integer jobId);

    /**
     * 根据工作流ID查询调度任务
     *
     * @param workflowId 工作流ID
     * @return JobInfo
     */
    RequestReturnVO getWorkflowScheduleJob(Integer workflowId);

    /**
     * 根据JobId查询调度任务
     *
     * @param jobId 任务ID
     * @return JobInfo
     */
    RequestReturnVO getScheduleJobById(Integer jobId);

    /**
     * 检查工作流是否已有调度任务
     *
     * @param workflowId 工作流ID
     * @return 是否存在
     */
    RequestReturnVO hasWorkflowScheduleJob(Integer workflowId);

    /**
     * 查询工作流调度任务详情
     * <p>
     * 功能说明：
     * 返回工作流主任务和所有节点任务的详细信息
     *
     * @param workflowId 工作流ID
     * @return 返回数据包含：workflowJob(工作流主任务), nodeJobs(节点任务列表), summary(统计信息)
     */
    RequestReturnVO getWorkflowScheduleDetails(Integer workflowId);
}
