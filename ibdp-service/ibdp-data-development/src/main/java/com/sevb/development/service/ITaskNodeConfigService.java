package com.sevb.development.service;

import com.sevb.common.util.RequestReturnVO;
import com.sevb.development.entity.dto.InsertNodeConfigDTO;
import com.sevb.development.entity.vo.TaskNodeConfigVO;

public interface ITaskNodeConfigService {

    /**
     * 根据任务节点ID查找当前节点配置信息
     *
     * @param nodeId 任务节点ID
     * @return com.sevb.common.util.RequestReturnVO
     */
    RequestReturnVO getActiveDataByNodeId(Integer nodeId);

    /**
     * 根据任务节点ID和版本号查找节点配置信息
     *
     * @param nodeId  任务节点ID
     * @param version 版本号
     * @return com.sevb.common.util.RequestReturnVO
     */
    RequestReturnVO getTaskNodeConfigByUnique(Integer nodeId, Integer version);

    /**
     * 新增任务节点配置信息
     *
     * @param form 任务节点配置信息表单
     * @return com.sevb.common.util.RequestReturnVO
     */
    RequestReturnVO insertTaskNodeConfig(InsertNodeConfigDTO form);

    /**
     * 根据节点ID获取节点的版本历史
     *
     * @param nodeId 任务节点ID
     * @return com.sevb.common.util.RequestReturnVO
     */
    RequestReturnVO findListByNodeId(Integer nodeId);

    /**
     * 还原任务节点的配置版本
     *
     * @param nodeId        任务节点ID
     * @param activeVersion 待还原的版本号
     * @return com.sevb.common.util.RequestReturnVO
     */
    RequestReturnVO restoreTaskNodeConfig(Integer nodeId, Integer activeVersion);

    /**
     * 提交任务节点配置信息
     *
     * @param nodeId 任务节点ID
     * @return com.sevb.common.util.RequestReturnVO
     */
    RequestReturnVO submitTaskNodeConfig(Integer nodeId);

}
