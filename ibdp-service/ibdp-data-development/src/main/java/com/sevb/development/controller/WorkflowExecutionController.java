package com.sevb.development.controller;

import com.sevb.common.util.RequestReturnVO;
import com.sevb.development.service.IWorkflowExecutionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 工作流执行控制器
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Slf4j
@RestController
@RequestMapping("/workflow/execution")
@Api(tags = "工作流实例管理")
public class WorkflowExecutionController {

    @Resource
    private IWorkflowExecutionService workflowExecutionService;


    @PostMapping("/pause")
    @ApiOperation("暂停工作流执行")
    public RequestReturnVO pauseWorkflowExecution(
            @ApiParam("工作流实例ID") @RequestParam Integer instanceId,
            @ApiParam("暂停用户") @RequestParam(required = false) String pauseUser) {

        log.info("暂停工作流执行，instanceId: {}, pauseUser: {}", instanceId, pauseUser);
        return workflowExecutionService.pauseWorkflowExecution(instanceId, pauseUser);
    }

    @PostMapping("/resume")
    @ApiOperation("恢复工作流执行")
    public RequestReturnVO resumeWorkflowExecution(
            @ApiParam("工作流实例ID") @RequestParam Integer instanceId,
            @ApiParam("恢复用户") @RequestParam(required = false) String resumeUser) {

        log.info("恢复工作流执行，instanceId: {}, resumeUser: {}", instanceId, resumeUser);
        return workflowExecutionService.resumeWorkflowExecution(instanceId, resumeUser);
    }

    @GetMapping("/instance")
    @ApiOperation("查询工作流实例详情")
    public RequestReturnVO getWorkflowInstanceDetail(
            @ApiParam("工作流实例ID") @RequestParam Integer instanceId) {

        return workflowExecutionService.getWorkflowInstanceDetail(instanceId);
    }

    @GetMapping("/instance/nodes")
    @ApiOperation("查询任务实例列表")
    public RequestReturnVO getTaskNodeInstances(
            @ApiParam("工作流实例ID") @RequestParam(required = false) Integer instanceId,
            @ApiParam("搜索条件(任务名称/任务ID/实例ID)") @RequestParam(required = false) String searchKeyword,
            @ApiParam("开始时间") @RequestParam(required = false) String startTime,
            @ApiParam("结束时间") @RequestParam(required = false) String endTime,
            @ApiParam("调度主机") @RequestParam(required = false) String executorAddress,
            @ApiParam("责任人") @RequestParam(required = false) String responsiblePerson,
            @ApiParam("任务类型(子类型)") @RequestParam(required = false) String taskType,
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("页大小") @RequestParam(defaultValue = "20") Integer pageSize) {

        return workflowExecutionService.getTaskNodeInstances(instanceId, searchKeyword, startTime, endTime,
                executorAddress, responsiblePerson, taskType, pageNum, pageSize);
    }

    @GetMapping("/instance/progress")
    @ApiOperation("查询工作流实例执行进度")
    public RequestReturnVO getWorkflowExecutionProgress(
            @ApiParam("工作流实例ID（可选，如果不传则获取最新实例）") @RequestParam(required = false) Integer instanceId,
            @ApiParam("工作流ID（当instanceId为空时必传）") @RequestParam(required = false) Integer workflowId) {

        return workflowExecutionService.getWorkflowExecutionProgress(instanceId, workflowId);
    }

    @GetMapping("/instances")
    @ApiOperation("查询工作流实例列表")
    public RequestReturnVO getWorkflowInstances(
            @ApiParam("工作流ID") @RequestParam(required = false) Integer workflowId,
            @ApiParam("业务流程名称") @RequestParam(required = false) String workflowName,
            @ApiParam("状态") @RequestParam(required = false) String status,
            @ApiParam("开始日期") @RequestParam(required = false) String startDate,
            @ApiParam("结束日期") @RequestParam(required = false) String endDate,
            @ApiParam("责任人") @RequestParam(required = false) String responsiblePerson,
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("页大小") @RequestParam(defaultValue = "20") Integer pageSize) {

        return workflowExecutionService.getWorkflowInstances(workflowId, workflowName, status,
            startDate, endDate, responsiblePerson, pageNum, pageSize);
    }

    @PostMapping("/node/retry")
    @ApiOperation("重试失败的节点")
    public RequestReturnVO retryFailedNode(
            @ApiParam("节点实例ID") @RequestParam Integer nodeInstanceId,
            @ApiParam("重试用户") @RequestParam(required = false) String retryUser) {

        log.info("重试失败的节点，nodeInstanceId: {}, retryUser: {}", nodeInstanceId, retryUser);
        return workflowExecutionService.retryFailedNode(nodeInstanceId, retryUser);
    }
}
