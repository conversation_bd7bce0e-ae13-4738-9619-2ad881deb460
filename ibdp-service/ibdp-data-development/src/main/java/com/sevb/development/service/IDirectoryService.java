package com.sevb.development.service;

import com.sevb.development.entity.dto.InsertDirectoryDTO;
import com.sevb.common.util.RequestReturnVO;
import com.sevb.development.entity.dto.UpdateDirectoryDto;


public interface IDirectoryService {

    /**
     * 新增目录数据
     *
     * @param insertDirectoryDto 目录数据
     * @return RequestReturnVO
     */
    RequestReturnVO addDirectoryData(InsertDirectoryDTO insertDirectoryDto);

    /**
     * 修改目录数据
     *
     * @param updateDirectoryDto 目录数据
     * @return RequestReturnVO
     */
    RequestReturnVO editDirectoryData(UpdateDirectoryDto updateDirectoryDto);

    /**
     * 删除目录数据
     *
     * @param directoryId 目录ID
     * @return RequestReturnVO
     */
    RequestReturnVO removeDirectoryData(Integer directoryId);

    /**
     * 根据ID查询目录
     *
     * @param directoryId 目录id
     * @return RequestReturnVO
     */
    RequestReturnVO selectDirectoryById(Integer directoryId);

    /**
     * 根据项目ID查询目录列表
     *
     * @param projectId 项目ID
     * @return RequestReturnVO
     */
    RequestReturnVO selectDirectoryByProjectId(String projectId);

}
