package com.sevb.development.service;

import com.sevb.common.util.RequestReturnVO;
import com.sevb.development.entity.vo.SqlRunInputVO;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("/sqlRun")
public interface ISqlRunService {

    /**
     * 数据开发类-执行SQL脚本
     */
    @POST
    @Path("runSqlByType")
    RequestReturnVO runSqlByType (SqlRunInputVO sqlRunInputVO) throws Exception;

}