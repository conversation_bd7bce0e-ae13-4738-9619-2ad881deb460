package com.sevb.development.controller;

import com.sevb.common.util.RequestReturnVO;
import com.sevb.development.service.ITaskNodeVersionService;
import io.swagger.annotations.*;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@Api(tags = "节点版本管理")
@RequestMapping("/nodeVersion")
public class TaskNodeVersionController {

    @Resource
    private ITaskNodeVersionService taskNodeVersionService;

    @PutMapping("/restore")
    @ApiOperation(value = "重置任务节点版本")
    @ApiImplicitParams({@ApiImplicitParam(name = "nodeId", value = "任务节点ID", required = true, dataType = "Integer", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "activeVersion", value = "待重置的版本号", required = true, dataType = "Integer", dataTypeClass = Integer.class)
    })
    public RequestReturnVO resetActiveVersion(@RequestParam(value = "nodeId") Integer nodeId,
                                              @RequestParam(value = "activeVersion") Integer activeVersion) {
        return taskNodeVersionService.resetActiveVersion(nodeId, activeVersion);
    }

    @GetMapping("/history")
    @ApiOperation(value = "获取任务节点的版本历史")
    @ApiImplicitParam(name = "nodeId", value = "任务节点ID", required = true, dataType = "Integer", dataTypeClass = Integer.class)
    public RequestReturnVO listHistoryList(@RequestParam(value = "nodeId") Integer nodeId) {
        return taskNodeVersionService.findListByNodeId(nodeId);
    }

    @GetMapping("/getNodeVersionByUnique")
    @ApiOperation(value = "获取任务节点的指定版本")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "nodeId", value = "任务节点ID", required = true, dataType = "Integer", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "version", value = "待获取的版本号", required = true, dataType = "Integer", dataTypeClass = Integer.class)
    })
    public RequestReturnVO getTaskNodeVersionByUnique(@RequestParam(value = "nodeId") Integer nodeId,
                                                      @RequestParam(value = "version") Integer version) {
        return taskNodeVersionService.getTaskNodeVersionByUnique(nodeId, version);
    }
}
