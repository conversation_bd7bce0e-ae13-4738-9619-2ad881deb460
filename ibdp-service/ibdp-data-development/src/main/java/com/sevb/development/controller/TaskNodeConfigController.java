package com.sevb.development.controller;

import com.sevb.common.util.RequestReturnVO;
import com.sevb.development.entity.dto.InsertNodeConfigDTO;
import com.sevb.development.entity.vo.TaskNodeConfigVO;
import com.sevb.development.service.ITaskNodeConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags = "任务节点配置管理")
@Controller
@RequestMapping("/nodeConfig")
public class TaskNodeConfigController {

    @Resource
    private ITaskNodeConfigService taskNodeConfigService;

    @GetMapping("/current")
    @ResponseBody
    @ApiOperation(value = "获取任务节点的当前配置")
    @ApiImplicitParam(name = "nodeId", value = "任务节点ID", required = true, dataType = "Integer", dataTypeClass = Integer.class)
    public RequestReturnVO getActiveDataByNodeId(@RequestParam(value = "nodeId") Integer nodeId) {
        return this.taskNodeConfigService.getActiveDataByNodeId(nodeId);
    }

    @GetMapping("/version")
    @ResponseBody
    @ApiOperation(value = "根据任务节点ID和版本号查找节点配置信息")
    @ApiImplicitParams({@ApiImplicitParam(name = "nodeId", value = "任务节点ID", required = true, dataType = "Integer", dataTypeClass = Integer.class),
                        @ApiImplicitParam(name = "version", value = "版本号", required = true, dataType = "Integer", dataTypeClass = Integer.class)})
    public RequestReturnVO getTaskNodeConfigByUnique(@RequestParam(value = "nodeId") Integer nodeId,
                                                     @RequestParam(value = "version") Integer version) {
        return this.taskNodeConfigService.getTaskNodeConfigByUnique(nodeId, version);
    }

    @PostMapping("/save")
    @ResponseBody
    @ApiOperation(value = "保存任务节点配置")
    @ApiImplicitParam(name = "form", value = "任务节点配置表单", required = true, dataType = "InsertNodeConfigDTO", dataTypeClass = InsertNodeConfigDTO.class)
    public RequestReturnVO insertTaskNodeConfig(@RequestBody InsertNodeConfigDTO form) {
        return this.taskNodeConfigService.insertTaskNodeConfig(form);
    }

    @GetMapping("/history")
    @ResponseBody
    @ApiOperation(value = "获取任务节点的版本历史")
    @ApiImplicitParam(name = "nodeId", value = "任务节点ID", required = true, dataType = "Integer", dataTypeClass = Integer.class)
    public RequestReturnVO listHistoryList(@RequestParam(value = "nodeId") Integer nodeId) {
        return this.taskNodeConfigService.findListByNodeId(nodeId);
    }

    @PutMapping("/restore")
    @ResponseBody
    @ApiOperation(value = "还原任务节点的配置版本")
    @ApiImplicitParams({@ApiImplicitParam(name = "nodeId", value = "任务节点ID", required = true, dataType = "Integer", dataTypeClass = Integer.class),
                        @ApiImplicitParam(name = "activeVersion", value = "待还原的版本号", required = true, dataType = "Integer", dataTypeClass = Integer.class)})
    public RequestReturnVO restoreTaskNodeConfig(@RequestParam(value = "nodeId") Integer nodeId,
                                                 @RequestParam(value = "activeVersion") Integer activeVersion) {
        return this.taskNodeConfigService.restoreTaskNodeConfig(nodeId, activeVersion);
    }

    @PutMapping("/submit")
    @ResponseBody
    @ApiOperation(value = "提交任务节点的配置")
    @ApiImplicitParam(name = "nodeId", value = "任务节点ID", required = true, dataType = "Integer", dataTypeClass = Integer.class)
    public RequestReturnVO submitTaskNodeConfig(@RequestParam(value = "nodeId") Integer nodeId) {
        return this.taskNodeConfigService.submitTaskNodeConfig(nodeId);
    }

}
