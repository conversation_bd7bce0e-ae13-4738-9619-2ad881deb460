package com.sevb.development.service.impl;

import com.sevb.common.util.RequestReturnVO;
import com.sevb.development.dao.ITaskNodeDao;
import com.sevb.development.dao.ITaskNodeEdgesDao;
import com.sevb.development.entity.vo.TaskNodeEdgesVO;
import com.sevb.development.entity.vo.TaskNodeVO;
import com.sevb.development.service.ITaskNodeEdgesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TaskNodeEdgesServiceImpl implements ITaskNodeEdgesService {

    @Resource
    ITaskNodeEdgesDao taskNodeEdgesDao;

    @Resource
    private ITaskNodeDao taskNodeDao;

    @Override
    @Transactional
    public RequestReturnVO insertTaskNodeEdges(TaskNodeEdgesVO form) {
        try {
            // 1. 验证基本参数
            if (form == null || form.getWorkflowId() == null ||
                form.getSourceNodeId() == null || form.getTargetNodeId() == null) {
                return RequestReturnVO.fail("参数不能为空");
            }

            // 2. 验证节点是否存在
            RequestReturnVO validateResult = validateNodes(form.getWorkflowId(),
                form.getSourceNodeId(), form.getTargetNodeId());
            if (!"200".equals(validateResult.getStatus())) {
                return validateResult;
            }

            // 3. 检查是否已存在连线
            if (taskNodeEdgesDao.existsEdge(form.getWorkflowId(),
                form.getSourceNodeId(), form.getTargetNodeId())) {
                return RequestReturnVO.fail("节点连线已存在");
            }

            // 4. 检查是否会产生循环依赖
            RequestReturnVO circularCheckResult = wouldCreateCircularDependency(
                form.getWorkflowId(), form.getSourceNodeId(), form.getTargetNodeId());
            if (!"200".equals(circularCheckResult.getStatus())) {
                return circularCheckResult;
            }

            // 5. 设置默认值
            if (form.getId() == null || form.getId().trim().isEmpty()) {
                form.setId(UUID.randomUUID().toString());
            }
            if (form.getStartPoint() == null || form.getStartPoint().trim().isEmpty()) {
                form.setStartPoint("{\"x\": 0, \"y\": 0}");
            }
            if (form.getEndPoint() == null || form.getEndPoint().trim().isEmpty()) {
                form.setEndPoint("{\"x\": 0, \"y\": 0}");
            }

            // 6. 插入连线
            int result = taskNodeEdgesDao.insertTaskNodeEdges(form);
            if (result > 0) {
                log.info("成功添加节点连线: {} -> {}", form.getSourceNodeId(), form.getTargetNodeId());
                return RequestReturnVO.success(form);
            } else {
                return RequestReturnVO.fail("添加节点连线失败");
            }

        } catch (Exception e) {
            log.error("添加节点连线异常", e);
            return RequestReturnVO.fail("添加节点连线异常: " + e.getMessage());
        }
    }


    @Override
    @Transactional
    public RequestReturnVO removeTaskNodeEdges(String edgesId) {
        try {
            if (edgesId == null || edgesId.trim().isEmpty()) {
                return RequestReturnVO.fail("连线ID不能为空");
            }

            int result = taskNodeEdgesDao.removeTaskNodeEdge(edgesId);
            if (result > 0) {
                log.info("成功删除节点连线，edgeId: {}", edgesId);
                return RequestReturnVO.success(true);
            } else {
                return RequestReturnVO.fail("节点连线不存在或删除失败");
            }

        } catch (Exception e) {
            log.error("删除节点连线异常", e);
            return RequestReturnVO.fail("删除节点连线异常: " + e.getMessage());
        }
    }

    @Override
    public RequestReturnVO getTaskEdgesByWorkflowId(Integer workflowId) {
        return RequestReturnVO.success(taskNodeEdgesDao.getTaskEdgesByWorkflowId(workflowId));
    }

    // ==================== 从NodeDependencyServiceImpl迁移的方法实现 ====================







    @Override
    public RequestReturnVO getWorkflowDependencies(Integer workflowId) {
        try {
            List<TaskNodeEdgesVO> edges = taskNodeEdgesDao.getTaskEdgesByWorkflowId(workflowId);

            // 转换为依赖关系格式
            List<Map<String, Object>> dependencies = edges.stream()
                .map(edge -> {
                    Map<String, Object> dep = new HashMap<>();
                    dep.put("sourceNodeId", edge.getSourceNodeId());
                    dep.put("targetNodeId", edge.getTargetNodeId());
                    dep.put("edgeId", edge.getId());
                    return dep;
                })
                .collect(Collectors.toList());

            return RequestReturnVO.success(dependencies);

        } catch (Exception e) {
            log.error("查询工作流依赖关系异常", e);
            return RequestReturnVO.fail("查询工作流依赖关系异常: " + e.getMessage());
        }
    }

    @Override
    public RequestReturnVO getNodeUpstreamDependencies(Integer workflowId, Integer nodeId) {
        try {
            List<Integer> upstreamNodeIds = taskNodeEdgesDao.selectSourceNodeIdsByTargetNodeId(workflowId, nodeId);
            return RequestReturnVO.success(upstreamNodeIds);
        } catch (Exception e) {
            log.error("查询节点上游依赖异常", e);
            return RequestReturnVO.fail("查询节点上游依赖异常: " + e.getMessage());
        }
    }

    @Override
    public RequestReturnVO getNodeDownstreamDependencies(Integer workflowId, Integer nodeId) {
        try {
            List<Integer> downstreamNodeIds = taskNodeEdgesDao.selectTargetNodeIdsBySourceNodeId(workflowId, nodeId);
            return RequestReturnVO.success(downstreamNodeIds);
        } catch (Exception e) {
            log.error("查询节点下游依赖异常", e);
            return RequestReturnVO.fail("查询节点下游依赖异常: " + e.getMessage());
        }
    }

    @Override
    public RequestReturnVO checkCircularDependency(Integer workflowId) {
        try {
            List<TaskNodeEdgesVO> edges = taskNodeEdgesDao.getTaskEdgesByWorkflowId(workflowId);
            Map<Integer, List<Integer>> graph = new HashMap<>();
            for (TaskNodeEdgesVO edge : edges) {
                graph.computeIfAbsent(edge.getSourceNodeId(), k -> new ArrayList<>()).add(edge.getTargetNodeId());
            }

            Set<Integer> visited = new HashSet<>();
            Set<Integer> recursionStack = new HashSet<>();

            for (Integer nodeId : graph.keySet()) {
                if (!visited.contains(nodeId)) {
                    if (hasCycleDFS(nodeId, graph, visited, recursionStack)) {
                        return RequestReturnVO.success(true);
                    }
                }
            }
            return RequestReturnVO.success(false);
        } catch (Exception e) {
            log.error("检查循环依赖异常", e);
            return RequestReturnVO.fail("检查循环依赖异常: " + e.getMessage());
        }
    }

    @Override
    public RequestReturnVO getTopologicalSort(Integer workflowId) {
        try {
            List<TaskNodeEdgesVO> edges = taskNodeEdgesDao.getTaskEdgesByWorkflowId(workflowId);
            List<TaskNodeVO> allNodes = taskNodeDao.selectTaskNodesByWorkflowId(workflowId);
            Set<Integer> allNodeIds = allNodes.stream().map(TaskNodeVO::getNodeId).collect(Collectors.toSet());

            Map<Integer, List<Integer>> graph = new HashMap<>();
            Map<Integer, Integer> inDegree = new HashMap<>();

            for (Integer nodeId : allNodeIds) {
                graph.put(nodeId, new ArrayList<>());
                inDegree.put(nodeId, 0);
            }

            for (TaskNodeEdgesVO edge : edges) {
                graph.get(edge.getSourceNodeId()).add(edge.getTargetNodeId());
                inDegree.put(edge.getTargetNodeId(), inDegree.get(edge.getTargetNodeId()) + 1);
            }

            Queue<Integer> queue = new LinkedList<>();
            List<Integer> result = new ArrayList<>();

            for (Map.Entry<Integer, Integer> entry : inDegree.entrySet()) {
                if (entry.getValue() == 0) {
                    queue.offer(entry.getKey());
                }
            }

            while (!queue.isEmpty()) {
                Integer current = queue.poll();
                result.add(current);

                for (Integer neighbor : graph.get(current)) {
                    inDegree.put(neighbor, inDegree.get(neighbor) - 1);
                    if (inDegree.get(neighbor) == 0) {
                        queue.offer(neighbor);
                    }
                }
            }

            if (result.size() != allNodeIds.size()) {
                return RequestReturnVO.fail("存在循环依赖，无法进行拓扑排序");
            }
            return RequestReturnVO.success(result);
        } catch (Exception e) {
            log.error("获取拓扑排序异常", e);
            return RequestReturnVO.fail("获取拓扑排序异常: " + e.getMessage());
        }
    }

    @Override
    public RequestReturnVO getRootNodes(Integer workflowId) {
        try {
            List<Integer> rootNodeIds = taskNodeEdgesDao.selectRootNodeIds(workflowId);
            return RequestReturnVO.success(rootNodeIds);
        } catch (Exception e) {
            log.error("查询根节点异常", e);
            return RequestReturnVO.fail("查询根节点异常: " + e.getMessage());
        }
    }

    @Override
    public RequestReturnVO getLeafNodes(Integer workflowId) {
        try {
            List<Integer> leafNodeIds = taskNodeEdgesDao.selectLeafNodeIds(workflowId);
            return RequestReturnVO.success(leafNodeIds);
        } catch (Exception e) {
            log.error("查询叶子节点异常", e);
            return RequestReturnVO.fail("查询叶子节点异常: " + e.getMessage());
        }
    }

    @Override
    public RequestReturnVO validateDependency(Integer workflowId, Integer sourceNodeId, Integer targetNodeId) {
        try {
            RequestReturnVO validateResult = validateNodes(workflowId, sourceNodeId, targetNodeId);
            if (!"200".equals(validateResult.getStatus())) {
                return validateResult;
            }
            RequestReturnVO circularCheckResult = wouldCreateCircularDependency(workflowId, sourceNodeId, targetNodeId);
            if (!"200".equals(circularCheckResult.getStatus())) {
                return circularCheckResult;
            }
            return RequestReturnVO.success(true);
        } catch (Exception e) {
            log.error("验证依赖关系异常", e);
            return RequestReturnVO.fail("验证依赖关系异常: " + e.getMessage());
        }
    }

    // 辅助方法
    private RequestReturnVO validateNodes(Integer workflowId, Integer sourceNodeId, Integer targetNodeId) {
        if (workflowId == null || sourceNodeId == null || targetNodeId == null) {
            return RequestReturnVO.fail("参数不能为空");
        }
        if (sourceNodeId.equals(targetNodeId)) {
            return RequestReturnVO.fail("源节点和目标节点不能相同");
        }
        List<TaskNodeVO> nodes = taskNodeDao.selectTaskNodesByWorkflowId(workflowId);
        Set<Integer> nodeIds = nodes.stream().map(TaskNodeVO::getNodeId).collect(Collectors.toSet());
        if (!nodeIds.contains(sourceNodeId)) {
            return RequestReturnVO.fail("源节点不存在");
        }
        if (!nodeIds.contains(targetNodeId)) {
            return RequestReturnVO.fail("目标节点不存在");
        }
        return RequestReturnVO.success(true);
    }

    private RequestReturnVO wouldCreateCircularDependency(Integer workflowId, Integer sourceNodeId, Integer targetNodeId) {
        Set<Integer> visited = new HashSet<>();
        if (canReach(targetNodeId, sourceNodeId, workflowId, visited)) {
            return RequestReturnVO.fail("添加此依赖关系会产生循环依赖");
        }
        return RequestReturnVO.success(true);
    }

    private boolean canReach(Integer start, Integer end, Integer workflowId, Set<Integer> visited) {
        if (start.equals(end)) {
            return true;
        }
        if (visited.contains(start)) {
            return false;
        }
        visited.add(start);
        List<Integer> neighbors = taskNodeEdgesDao.selectTargetNodeIdsBySourceNodeId(workflowId, start);
        for (Integer neighbor : neighbors) {
            if (canReach(neighbor, end, workflowId, visited)) {
                return true;
            }
        }
        return false;
    }

    private boolean hasCycleDFS(Integer nodeId, Map<Integer, List<Integer>> graph,
                               Set<Integer> visited, Set<Integer> recursionStack) {
        visited.add(nodeId);
        recursionStack.add(nodeId);
        List<Integer> neighbors = graph.getOrDefault(nodeId, new ArrayList<>());
        for (Integer neighbor : neighbors) {
            if (!visited.contains(neighbor)) {
                if (hasCycleDFS(neighbor, graph, visited, recursionStack)) {
                    return true;
                }
            } else if (recursionStack.contains(neighbor)) {
                return true;
            }
        }
        recursionStack.remove(nodeId);
        return false;
    }


}