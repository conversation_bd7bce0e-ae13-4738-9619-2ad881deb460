<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:jaxrs="http://cxf.apache.org/jaxrs"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
       http://cxf.apache.org/jaxrs
       http://cxf.apache.org/schemas/jaxrs.xsd">
    <jaxrs:server id="datadevelopServiceRest" address="/datadevelop">
        <jaxrs:features>
            <ref bean = "demoFeatures" />
        </jaxrs:features>
        <jaxrs:serviceBeans>
            <ref bean="sqlRunService" />
        </jaxrs:serviceBeans>
        <jaxrs:providers>
            <ref bean="jsonProvider" />
        </jaxrs:providers>
    </jaxrs:server>
    <bean id="sqlRunService" class="com.sevb.development.service.impl.SqlRunService" />
    <bean id="jsonProvider" class="org.codehaus.jackson.jaxrs.JacksonJsonProvider" />
    <bean id="validationProvider" class="org.apache.cxf.validation.BeanValidationProvider" />
    <bean id="validationInInterceptor" class="org.apache.cxf.jaxrs.validation.JAXRSBeanValidationInInterceptor">
        <property name="provider" ref = "validationProvider" />
    </bean>

</beans>