<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sevb.development.dao.ITaskNodeDao">

    <insert id="insertTaskNodeData" parameterType="com.sevb.development.entity.vo.TaskNodeVO"
            useGeneratedKeys="true" keyProperty="nodeId">
        INSERT INTO public.t_ibdp_dd_task_node
                (node_name,
                node_type,
                child_type,
                remark,
                workflow_id,
                parent_node_id,
                content,
                job_id,
                x,
                y,
                create_by,
                create_time,
                update_by,
                update_time)
        VALUES (#{nodeName,jdbcType=VARCHAR},
                #{nodeType,jdbcType=VARCHAR},
                #{childType,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{workflowId,jdbcType=INTEGER},
                #{parentNodeId,jdbcType=INTEGER},
                #{content,jdbcType=VARCHAR},
                #{jobId,jdbcType=INTEGER},
                #{x,jdbcType=DOUBLE},
                #{y,jdbcType=DOUBLE},
                #{createBy,jdbcType=VARCHAR},
                LOCALTIMESTAMP,
                #{updateBy,jdbcType=VARCHAR},
                LOCALTIMESTAMP)
    </insert>

    <update id="updateTaskNodeData" parameterType="com.sevb.development.entity.vo.TaskNodeVO"
            useGeneratedKeys="true" keyProperty="nodeId">
        UPDATE public.t_ibdp_dd_task_node
        SET
        <if test="nodeName != null and nodeName != ''">
            node_name = #{nodeName,jdbcType=VARCHAR},
        </if>
        <if test="nodeType != null and nodeType != ''">
            node_type = #{nodeType,jdbcType=VARCHAR},
        </if>
        <if test="childType != null and childType != ''">
            child_type = #{childType,jdbcType=VARCHAR},
        </if>
        <if test="remark != null and remark != ''">
            remark = #{remark,jdbcType=VARCHAR},
        </if>
        <if test="workflowId != null and workflowId != ''">
            workflow_id = #{workflowId,jdbcType=INTEGER},
        </if>
        <if test="parentNodeId != null and parentNodeId != ''">
            parent_node_id = #{parentNodeId,jdbcType=INTEGER},
        </if>
        <if test="jobId != null and jobId != ''">
            job_id = #{jobId,jdbcType=INTEGER},
        </if>
        <if test="x != null and x != ''">
            x = #{x,jdbcType=DOUBLE},
        </if>
        <if test="y != null and y != ''">
            y = #{y,jdbcType=DOUBLE},
        </if>
        <if test="content != null and content != ''">
            content = #{content,jdbcType=VARCHAR},
        </if>
        <if test="updateBy != null and updateBy != ''">
            update_by = #{updateBy,jdbcType=VARCHAR},
        </if>

        <if test="cdsId != null and cdsId != ''">
            cds_id = #{cdsId,jdbcType=VARCHAR},
        </if>
        <if test="tdsId != null and tdsId != ''">
            tds_id = #{tdsId,jdbcType=INTEGER},
        </if>
        <if test="tableFrom != null and tableFrom != ''">
            table_from = #{tableFrom,jdbcType=INTEGER},
        </if>
        <if test="tableTo != null and tableTo != ''">
            table_to = #{tableTo,jdbcType=INTEGER},
        </if>
        <if test="dataFilter != null and dataFilter != ''">
            data_filter = #{dataFilter,jdbcType=DOUBLE},
        </if>
        <if test="splitColumn != null and splitColumn != ''">
            split_column = #{splitColumn,jdbcType=DOUBLE},
        </if>
        <if test="partitionColumn != null and partitionColumn != ''">
            partition_column = #{partitionColumn,jdbcType=VARCHAR},
        </if>
        <if test="writerMode != null and writerMode != ''">
            writer_mode = #{writerMode,jdbcType=VARCHAR},
        </if>
        <if test="nullToNull != null">
            null_to_null = #{nullToNull,jdbcType=BOOLEAN},
        </if>
        update_time = LOCALTIMESTAMP

        WHERE node_id = #{nodeId,jdbcType=INTEGER}
    </update>

    <update id="batchUpdateTaskNodes" parameterType="java.util.List">
        <foreach collection="taskNodes" item="taskNode" separator=";">
            UPDATE t_ibdp_dd_task_node
            <set>
                <if test="taskNode.nodeName != null">node_name = #{taskNode.nodeName},</if>
                <if test="taskNode.x != null">x = #{taskNode.x},</if>
                <if test="taskNode.y != null">y = #{taskNode.y},</if>
                update_time = NOW()
            </set>
            WHERE node_id = #{taskNode.nodeId}
        </foreach>
    </update>

    <delete id="deleteTaskNodeData">
        DELETE FROM public.t_ibdp_dd_task_node
        WHERE node_id = #{nodeId,jdbcType=INTEGER}
    </delete>

    <select id="selectTaskNodeById" resultType="com.sevb.development.entity.vo.TaskNodeVO">
        SELECT
            node.node_id,
            node.node_name,
            node.node_type,
            node.child_type,
            node.remark,
            node.workflow_id,
            workflow.workflow_name,
            node.parent_node_id,
            node.content,
            node.job_id,
            node.x,
            node.y,
            node.create_by,
            node.create_time,
            node.update_by,
            node.update_time,
            node.cds_id,
            node.tds_id,
            node.table_from,
            node.table_to,
            node.partition_column,
            node.split_column,
            node.writer_mode,
            node.data_filter,
            node.null_to_null
        FROM public.t_ibdp_dd_task_node node
        LEFT JOIN public.t_ibdp_dd_workflow workflow ON node.workflow_id = workflow.workflow_id
        WHERE node_id = #{nodeId,jdbcType=INTEGER}
    </select>

    <select id="selectTaskNodesByWorkflowId" resultType="com.sevb.development.entity.vo.TaskNodeVO">
        SELECT node_id,
               node_name,
               node_type,
               child_type,
               remark,
               workflow_id,
               parent_node_id,
               content,
               job_id,
               x,
               y,
               create_by,
               create_time,
               update_by,
               update_time
        FROM public.t_ibdp_dd_task_node
        WHERE workflow_id = #{workflowId,jdbcType=INTEGER}
    </select>
    <select id="selectTaskNodesByWorkflowIdAndNodeIds" resultType="com.sevb.development.entity.vo.TaskNodeVO">
        SELECT node_id,
        node_name,
        node_type,
        child_type,
        remark,
        workflow_id,
        parent_node_id,
        content,
        job_id,
        x,
        y,
        create_by,
        create_time,
        update_by,
        update_time
        FROM public.t_ibdp_dd_task_node
        WHERE workflow_id = #{workflowId,jdbcType=INTEGER}
        AND node_id IN
        <foreach item="item" collection="nodeIds" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
</mapper>