<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sevb.development.dao.IDirectoryDao">
    <insert id="insertDirectoryData" parameterType="com.sevb.development.entity.vo.DirectoryVO"
            useGeneratedKeys="true" keyProperty="directoryId">
        INSERT INTO public.t_ibdp_dd_directory (
        directory_name,
        parent_directory_id,
        project_id,
        create_by,
        create_time,
        update_by,
        update_time
        ) VALUES (#{directoryName,jdbcType=VARCHAR},
        #{parentDirectoryId,jdbcType=INTEGER},
        #{projectId,jdbcType=VARCHAR},
        #{createBy,jdbcType=VARCHAR},
        LOCALTIMESTAMP,
        #{updateBy,jdbcType=VARCHAR},
        LOCALTIMESTAMP);
    </insert>

    <select id="selectDirectoryById" parameterType="int" resultType="com.sevb.development.entity.po.DirectoryPO">
        SELECT directory_id,
        directory_name,
        parent_directory_id,
        project_id,
        create_by,
        create_time,
        update_by,
        update_time
        FROM public.t_ibdp_dd_directory
        WHERE directory_id = #{directoryId};
    </select>

    <select id="selectDirectoryByProjectId" resultType="com.sevb.development.entity.po.DirectoryPO">
        SELECT directory_id,
        directory_name,
        parent_directory_id,
        project_id,
        create_by,
        create_time,
        update_by,
        update_time
        FROM public.t_ibdp_dd_directory
        WHERE project_id = #{projectId,jdbcType=VARCHAR}
        ORDER BY parent_directory_id, directory_id;
    </select>

    <select id="selectDirectoryByParentDirectoryId" resultType="com.sevb.development.entity.po.DirectoryPO">
        SELECT directory_id,
        directory_name,
        parent_directory_id,
        project_id,
        create_by,
        create_time,
        update_by,
        update_time
        FROM public.t_ibdp_dd_directory
        WHERE parent_directory_id = #{parentDirectoryId,jdbcType=INTEGER}
        ORDER BY parent_directory_id, directory_id;
    </select>

    <update id="updateDirectoryData" parameterType="com.sevb.development.entity.vo.DirectoryVO"
            useGeneratedKeys="true" keyProperty="directoryId">
        UPDATE public.t_ibdp_dd_directory
        SET
        <if test="directoryName != null and directoryName != ''">
            directory_name = #{directoryName,jdbcType=VARCHAR},
        </if>
        <if test="parentDirectoryId != null and parentDirectoryId != ''">
            parent_directory_id = #{parentDirectoryId,jdbcType=INTEGER},
        </if>
        <if test="projectId != null and projectId != ''">
            project_id = #{projectId,jdbcType=VARCHAR},
        </if>
        <if test="updateBy != null and updateBy != ''">
            update_by = #{updateBy,jdbcType=VARCHAR},
        </if>
        update_time = LOCALTIMESTAMP
        WHERE directory_id = #{directoryId,jdbcType=INTEGER};
    </update>

    <delete id="deleteDirectoryData" parameterType="int">
        DELETE FROM public.t_ibdp_dd_directory
        WHERE directory_id = #{directoryId,jdbcType=INTEGER};
    </delete>
</mapper>