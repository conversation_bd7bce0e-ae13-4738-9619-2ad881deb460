<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sevb.development.dao.ITaskNodeEdgesDao">

    <insert id="insertTaskNodeEdges" parameterType="com.sevb.development.entity.vo.TaskNodeEdgesVO"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO public.t_ibdp_dd_task_edges
        (id, workflow_id, source_node_id, target_node_id, start_point,
         end_point, points_list, properties, create_by, create_time, update_by, update_time)
        VALUES (#{id,jdbcType=VARCHAR},
                #{workflowId,jdbcType=INTEGER},
                #{sourceNodeId,jdbcType=INTEGER},
                #{targetNodeId,jdbcType=INTEGER},
                CASE
                    WHEN #{startPoint,jdbcType=VARCHAR} IS NULL OR #{startPoint,jdbcType=VARCHAR} = '' THEN NULL::json
                    ELSE #{startPoint,jdbcType=VARCHAR}::json
                END,
                CASE
                    WHEN #{endPoint,jdbcType=VARCHAR} IS NULL OR #{endPoint,jdbcType=VARCHAR} = '' THEN NULL::json
                    ELSE #{endPoint,jdbcType=VARCHAR}::json
                END,
                CASE
                    WHEN #{pointsList,jdbcType=VARCHAR} IS NULL OR #{pointsList,jdbcType=VARCHAR} = '' THEN NULL::json
                    ELSE #{pointsList,jdbcType=VARCHAR}::json
                END,
                CASE
                    WHEN #{properties,jdbcType=VARCHAR} IS NULL OR #{properties,jdbcType=VARCHAR} = '' THEN NULL::json
                    ELSE #{properties,jdbcType=VARCHAR}::json
                END,
                #{createBy,jdbcType=VARCHAR},
                LOCALTIMESTAMP,
                #{updateBy,jdbcType=VARCHAR},
                LOCALTIMESTAMP)
    </insert>
    <update id="batchUpdateTaskNodeEdges">
        <foreach collection="edges" item="edge" separator=";">
            UPDATE t_ibdp_dd_task_edges
            <set>
                <if test="edge.workflowId != null">workflow_id = #{edge.workflowId},</if>
                <if test="edge.sourceNodeId != null">source_node_id = #{edge.sourceNodeId},</if>
                <if test="edge.targetNodeId != null">target_node_id = #{edge.targetNodeId},</if>
                <if test="edge.startPoint != null">
                    start_point = #{edge.startPoint}::json,
                </if>
                <if test="edge.endPoint != null">
                    end_point = #{edge.endPoint}::json,
                </if>
                <if test="edge.pointsList != null">
                    points_list = #{edge.pointsList}::json,
                </if>
                <if test="edge.properties != null">
                    properties = #{edge.properties}::json,
                </if>
                <if test="edge.updateBy != null">update_by = #{edge.updateBy},</if>
                update_time = NOW()
            </set>
            WHERE id = #{edge.id}
        </foreach>
    </update>
    <delete id="removeTaskNodeEdge">
        DELETE
        FROM public.t_ibdp_dd_task_edges
        WHERE id = #{edgeId,jdbcType=INTEGER}
    </delete>

    <select id="getTaskEdgesByWorkflowId" resultType="com.sevb.development.entity.vo.TaskNodeEdgesVO">
        SELECT id,
               workflow_id,
               source_node_id,
               target_node_id,
               start_point,
               end_point,
               points_list,
               properties,
               create_by,
               create_time,
               update_by,
               update_time
        FROM public.t_ibdp_dd_task_edges
        WHERE workflow_id = #{workflowId,jdbcType=INTEGER}
    </select>
    <select id="getTaskEdgesByWorkflowIdAndEdgeIds"
            resultType="com.sevb.development.entity.vo.TaskNodeEdgesVO">
        SELECT id,
        workflow_id,
        source_node_id,
        target_node_id,
        start_point,
        end_point,
        points_list,
        properties,
        create_by,
        create_time,
        update_by,
        update_time
        FROM public.t_ibdp_dd_task_edges
        WHERE workflow_id = #{workflowId,jdbcType=INTEGER}
        AND id IN
        <foreach collection="edgeIds" item="edgeId" open="(" separator="," close=")">
            #{edgeId}
        </foreach>
    </select>

    <!-- ==================== 从NodeDependencyDao迁移的SQL方法 ==================== -->



    <!-- 根据源节点ID查询依赖它的目标节点 -->
    <select id="selectTargetNodeIdsBySourceNodeId" resultType="java.lang.Integer">
        SELECT target_node_id
        FROM public.t_ibdp_dd_task_edges
        WHERE workflow_id = #{workflowId,jdbcType=INTEGER}
          AND source_node_id = #{sourceNodeId,jdbcType=INTEGER}
    </select>

    <!-- 根据目标节点ID查询其依赖的源节点 -->
    <select id="selectSourceNodeIdsByTargetNodeId" resultType="java.lang.Integer">
        SELECT source_node_id
        FROM public.t_ibdp_dd_task_edges
        WHERE workflow_id = #{workflowId,jdbcType=INTEGER}
          AND target_node_id = #{targetNodeId,jdbcType=INTEGER}
    </select>

    <!-- 检查两个节点之间是否存在连线 -->
    <select id="existsEdge" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM public.t_ibdp_dd_task_edges
        WHERE workflow_id = #{workflowId,jdbcType=INTEGER}
          AND source_node_id = #{sourceNodeId,jdbcType=INTEGER}
          AND target_node_id = #{targetNodeId,jdbcType=INTEGER}
    </select>



    <!-- 根据工作流ID删除所有连线 -->
    <delete id="deleteTaskNodeEdgesByWorkflowId">
        DELETE FROM public.t_ibdp_dd_task_edges
        WHERE workflow_id = #{workflowId,jdbcType=INTEGER}
    </delete>

    <!-- 根据节点ID删除相关的所有连线 -->
    <delete id="deleteTaskNodeEdgesByNodeId">
        DELETE FROM public.t_ibdp_dd_task_edges
        WHERE source_node_id = #{nodeId,jdbcType=INTEGER}
           OR target_node_id = #{nodeId,jdbcType=INTEGER}
    </delete>

    <!-- 查询工作流中的根节点(没有依赖的节点) -->
    <select id="selectRootNodeIds" resultType="java.lang.Integer">
        SELECT DISTINCT n.node_id
        FROM public.t_ibdp_dd_task_node n
        WHERE n.workflow_id = #{workflowId,jdbcType=INTEGER}
          AND n.node_id NOT IN (
              SELECT DISTINCT target_node_id
              FROM public.t_ibdp_dd_task_edges
              WHERE workflow_id = #{workflowId,jdbcType=INTEGER}
          )
    </select>

    <!-- 查询工作流中的叶子节点(没有被依赖的节点) -->
    <select id="selectLeafNodeIds" resultType="java.lang.Integer">
        SELECT DISTINCT n.node_id
        FROM public.t_ibdp_dd_task_node n
        WHERE n.workflow_id = #{workflowId,jdbcType=INTEGER}
          AND n.node_id NOT IN (
              SELECT DISTINCT source_node_id
              FROM public.t_ibdp_dd_task_edges
              WHERE workflow_id = #{workflowId,jdbcType=INTEGER}
          )
    </select>
</mapper>