<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sevb.development.dao.ITaskNodeVersionDao">

    <insert id="insertTaskNodeVersionData" parameterType="com.sevb.development.entity.vo.TaskNodeVersionVO">
        <selectKey resultType="Integer" keyColumn="version" keyProperty="version" order="BEFORE">
            SELECT COALESCE(MAX(VERSION), 0)+1 AS version FROM public.t_ibdp_dd_task_node_version WHERE node_id = #{nodeId, jdbcType=INTEGER}
        </selectKey>
        INSERT INTO public.t_ibdp_dd_task_node_version (
        node_id,
        version,
        status,
        content,
        remark,
        created_by,
        creation_date,
        last_updated_by,
        last_update_date
        ) VALUES (
        #{nodeId,jdbcType=INTEGER},
        #{version,jdbcType=INTEGER},
        'Y',
        #{content,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{createdBy,jdbcType=VARCHAR},
        CURRENT_TIMESTAMP,
        #{createdBy,jdbcType=VARCHAR},
        CURRENT_TIMESTAMP
        )
    </insert>

    <update id="resetActiveVersion">
        UPDATE public.t_ibdp_dd_task_node_version
        SET status = (CASE version WHEN #{activeVersion, jdbcType=INTEGER} THEN 'Y' ELSE 'N' END)
        WHERE node_id = #{nodeId, jdbcType=INTEGER}
          AND (status = 'Y' OR version = #{activeVersion, jdbcType=INTEGER})
    </update>

    <select id="findListByNodeId" resultType="com.sevb.development.entity.vo.TaskNodeVersionVO">
        SELECT version.node_id,
               version.version,
               version.status,
               version.content,
               version.remark,
               version.created_by,
               version.creation_date,
               version.last_updated_by,
               version.last_update_date,
               node.node_name
        FROM public.t_ibdp_dd_task_node_version version
        LEFT JOIN public.t_ibdp_dd_task_node node on version.node_id = node.node_id
        WHERE version.node_id = #{nodeId, jdbcType=INTEGER}
        ORDER BY VERSION DESC
    </select>

    <select id="getTaskNodeVersionByUnique" resultType="com.sevb.development.entity.vo.TaskNodeVersionVO">
        SELECT version.node_id,
               version.version,
               version.status,
               version.content,
               version.remark,
               version.created_by,
               version.creation_date,
               version.last_updated_by,
               version.last_update_date,
               node.node_name
        FROM public.t_ibdp_dd_task_node_version version
        LEFT JOIN public.t_ibdp_dd_task_node node on version.node_id = node.node_id
        WHERE version.node_id = #{nodeId, jdbcType=INTEGER}
          AND version.version = #{version, jdbcType=INTEGER}
    </select>
</mapper>