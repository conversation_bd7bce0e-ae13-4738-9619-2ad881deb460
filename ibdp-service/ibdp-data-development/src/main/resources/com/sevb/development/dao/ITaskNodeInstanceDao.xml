<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sevb.development.dao.ITaskNodeInstanceDao">

    <!-- 结果映射 -->
    <resultMap id="taskNodeInstanceResultMap" type="com.sevb.development.entity.po.TaskNodeInstancePO">
        <id column="node_instance_id" property="nodeInstanceId" jdbcType="INTEGER"/>
        <result column="workflow_instance_id" property="workflowInstanceId" jdbcType="INTEGER"/>
        <result column="node_id" property="nodeId" jdbcType="INTEGER"/>
        <result column="node_name" property="nodeName" jdbcType="VARCHAR"/>
        <result column="node_type" property="nodeType" jdbcType="VARCHAR"/>
        <result column="child_type" property="childType" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="duration_ms" property="durationMs" jdbcType="BIGINT"/>
        <result column="job_log_id" property="jobLogId" jdbcType="BIGINT"/>
        <result column="executor_address" property="executorAddress" jdbcType="VARCHAR"/>
        <result column="executor_handler" property="executorHandler" jdbcType="VARCHAR"/>
        <result column="executor_param" property="executorParam" jdbcType="VARCHAR"/>
        <result column="error_message" property="errorMessage" jdbcType="VARCHAR"/>
        <result column="retry_count" property="retryCount" jdbcType="INTEGER"/>
        <result column="max_retry_count" property="maxRetryCount" jdbcType="INTEGER"/>
        <result column="dependency_status" property="dependencyStatus" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <!-- 责任人 -->
        <result column="responsible_person" property="responsiblePerson" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 插入节点实例 -->
    <insert id="insertTaskNodeInstance" parameterType="com.sevb.development.entity.po.TaskNodeInstancePO"
            useGeneratedKeys="true" keyProperty="nodeInstanceId">
        INSERT INTO public.t_ibdp_dd_task_node_instance (
            workflow_instance_id, node_id, node_name, node_type, child_type, status, start_time, end_time,
            duration_ms, job_log_id, executor_address, executor_handler, executor_param,
            error_message, retry_count, max_retry_count, dependency_status
        ) VALUES (
            #{workflowInstanceId,jdbcType=INTEGER}, #{nodeId,jdbcType=INTEGER}, #{nodeName,jdbcType=VARCHAR},
            #{nodeType,jdbcType=VARCHAR}, #{childType,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{startTime,jdbcType=TIMESTAMP},
            #{endTime,jdbcType=TIMESTAMP}, #{durationMs,jdbcType=BIGINT}, #{jobLogId,jdbcType=BIGINT},
            #{executorAddress,jdbcType=VARCHAR}, #{executorHandler,jdbcType=VARCHAR}, #{executorParam,jdbcType=VARCHAR},
            #{errorMessage,jdbcType=VARCHAR}, #{retryCount,jdbcType=INTEGER}, #{maxRetryCount,jdbcType=INTEGER},
            #{dependencyStatus,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 批量插入节点实例 -->
    <insert id="batchInsertTaskNodeInstances" parameterType="java.util.List">
        INSERT INTO public.t_ibdp_dd_task_node_instance (
            workflow_instance_id, node_id, node_name, node_type, child_type, status, dependency_status, retry_count, max_retry_count
        ) VALUES
        <foreach collection="nodeInstances" item="item" separator=",">
            (#{item.workflowInstanceId,jdbcType=INTEGER}, #{item.nodeId,jdbcType=INTEGER},
             #{item.nodeName,jdbcType=VARCHAR}, #{item.nodeType,jdbcType=VARCHAR}, #{item.childType,jdbcType=VARCHAR},
             #{item.status,jdbcType=VARCHAR}, #{item.dependencyStatus,jdbcType=VARCHAR},
             #{item.retryCount,jdbcType=INTEGER}, #{item.maxRetryCount,jdbcType=INTEGER})
        </foreach>
    </insert>

    <!-- 根据节点实例ID查询节点实例 -->
    <select id="selectTaskNodeInstanceById" resultMap="taskNodeInstanceResultMap">
        SELECT node_instance_id, workflow_instance_id, node_id, node_name, node_type, child_type, status,
               start_time, end_time, duration_ms, job_log_id, executor_address, executor_handler,
               executor_param, error_message, retry_count, max_retry_count, dependency_status,
               create_time, update_time
        FROM public.t_ibdp_dd_task_node_instance
        WHERE node_instance_id = #{nodeInstanceId,jdbcType=INTEGER}
    </select>

    <!-- 根据工作流实例ID查询所有节点实例 -->
    <select id="selectTaskNodeInstancesByWorkflowInstanceId" resultMap="taskNodeInstanceResultMap">
        SELECT node_instance_id, workflow_instance_id, node_id, node_name, node_type, child_type, status,
               start_time, end_time, duration_ms, job_log_id, executor_address, executor_handler,
               executor_param, error_message, retry_count, max_retry_count, dependency_status,
               create_time, update_time
        FROM public.t_ibdp_dd_task_node_instance
        WHERE workflow_instance_id = #{workflowInstanceId,jdbcType=INTEGER}
        ORDER BY node_id
    </select>

    <!-- 根据工作流实例ID和状态查询节点实例 -->
    <select id="selectTaskNodeInstancesByWorkflowInstanceIdAndStatus" resultMap="taskNodeInstanceResultMap">
        SELECT node_instance_id, workflow_instance_id, node_id, node_name, node_type, child_type, status,
               start_time, end_time, duration_ms, job_log_id, executor_address, executor_handler,
               executor_param, error_message, retry_count, max_retry_count, dependency_status,
               create_time, update_time
        FROM public.t_ibdp_dd_task_node_instance
        WHERE workflow_instance_id = #{workflowInstanceId,jdbcType=INTEGER}
          AND status = #{status,jdbcType=VARCHAR}
        ORDER BY node_id
    </select>

    <!-- 查询就绪状态的节点实例 -->
    <select id="selectReadyTaskNodeInstances" resultMap="taskNodeInstanceResultMap">
        SELECT node_instance_id, workflow_instance_id, node_id, node_name, node_type, child_type, status,
               start_time, end_time, duration_ms, job_log_id, executor_address, executor_handler,
               executor_param, error_message, retry_count, max_retry_count, dependency_status,
               create_time, update_time
        FROM public.t_ibdp_dd_task_node_instance
        WHERE workflow_instance_id = #{workflowInstanceId,jdbcType=INTEGER}
          AND status = 'READY'
          AND dependency_status = 'SATISFIED'
        ORDER BY node_id
    </select>

    <!-- 查询运行中的节点实例 -->
    <select id="selectRunningTaskNodeInstances" resultMap="taskNodeInstanceResultMap">
        SELECT node_instance_id, workflow_instance_id, node_id, node_name, node_type, child_type, status,
               start_time, end_time, duration_ms, job_log_id, executor_address, executor_handler,
               executor_param, error_message, retry_count, max_retry_count, dependency_status,
               create_time, update_time
        FROM public.t_ibdp_dd_task_node_instance
        WHERE workflow_instance_id = #{workflowInstanceId,jdbcType=INTEGER}
          AND status = 'RUNNING'
        ORDER BY node_id
    </select>

    <!-- 分页查询节点实例列表 -->
    <select id="selectTaskNodeInstancesWithPaging" resultMap="taskNodeInstanceResultMap">
        SELECT tni.node_instance_id, tni.workflow_instance_id, tni.node_id, tni.node_name, tni.node_type, tni.child_type, tni.status,
               tni.start_time, tni.end_time, tni.duration_ms, tni.job_log_id, tni.executor_address, tni.executor_handler,
               tni.executor_param, tni.error_message, tni.retry_count, tni.max_retry_count, tni.dependency_status,
               tni.create_time, tni.update_time, ji.pic as responsible_person
        FROM public.t_ibdp_dd_task_node_instance tni
        LEFT JOIN public.t_ibdp_dd_workflow_instance wi ON tni.workflow_instance_id = wi.instance_id
        LEFT JOIN public.t_ibdp_dd_task_node tn ON tni.node_id = tn.node_id
        LEFT JOIN public.job_info ji ON tn.job_id = ji.id
        <where>
            <if test="instanceId != null">
                AND tni.workflow_instance_id = #{instanceId,jdbcType=INTEGER}
            </if>
            <if test="searchKeyword != null and searchKeyword != ''">
                AND (
                    tni.node_name LIKE CONCAT('%', #{searchKeyword,jdbcType=VARCHAR}, '%')
                    OR CAST(tni.node_id AS VARCHAR) LIKE CONCAT('%', #{searchKeyword,jdbcType=VARCHAR}, '%')
                    OR CAST(tni.node_instance_id AS VARCHAR) LIKE CONCAT('%', #{searchKeyword,jdbcType=VARCHAR}, '%')
                )
            </if>
            <if test="startTime != null">
                AND tni.start_time >= #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                AND tni.start_time &lt;= #{endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="executorAddress != null and executorAddress != ''">
                AND tni.executor_address LIKE CONCAT('%', #{executorAddress,jdbcType=VARCHAR}, '%')
            </if>
            <if test="responsiblePerson != null and responsiblePerson != ''">
                AND ji.pic LIKE CONCAT('%', #{responsiblePerson,jdbcType=VARCHAR}, '%')
            </if>
            <if test="taskType != null and taskType != ''">
                AND tni.child_type = #{taskType,jdbcType=VARCHAR}
            </if>
        </where>
        ORDER BY tni.create_time DESC
        LIMIT #{pageSize,jdbcType=INTEGER} OFFSET #{offset,jdbcType=INTEGER}
    </select>

    <!-- 统计节点实例数量 -->
    <select id="countTaskNodeInstances" resultType="int">
        SELECT COUNT(1)
        FROM public.t_ibdp_dd_task_node_instance tni
        LEFT JOIN public.t_ibdp_dd_workflow_instance wi ON tni.workflow_instance_id = wi.instance_id
        LEFT JOIN public.t_ibdp_dd_task_node tn ON tni.node_id = tn.node_id
        LEFT JOIN public.job_info ji ON tn.job_id = ji.id
        <where>
            <if test="instanceId != null">
                AND tni.workflow_instance_id = #{instanceId,jdbcType=INTEGER}
            </if>
            <if test="searchKeyword != null and searchKeyword != ''">
                AND (
                    tni.node_name LIKE CONCAT('%', #{searchKeyword,jdbcType=VARCHAR}, '%')
                    OR CAST(tni.node_id AS VARCHAR) LIKE CONCAT('%', #{searchKeyword,jdbcType=VARCHAR}, '%')
                    OR CAST(tni.node_instance_id AS VARCHAR) LIKE CONCAT('%', #{searchKeyword,jdbcType=VARCHAR}, '%')
                )
            </if>
            <if test="startTime != null">
                AND tni.start_time >= #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                AND tni.start_time &lt;= #{endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="executorAddress != null and executorAddress != ''">
                AND tni.executor_address LIKE CONCAT('%', #{executorAddress,jdbcType=VARCHAR}, '%')
            </if>
            <if test="responsiblePerson != null and responsiblePerson != ''">
                AND ji.pic LIKE CONCAT('%', #{responsiblePerson,jdbcType=VARCHAR}, '%')
            </if>
            <if test="taskType != null and taskType != ''">
                AND tni.child_type = #{taskType,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <!-- 更新节点实例状态 -->
    <update id="updateTaskNodeInstanceStatus">
        UPDATE public.t_ibdp_dd_task_node_instance
        SET status = #{status,jdbcType=VARCHAR},
            error_message = #{errorMessage,jdbcType=VARCHAR},
            update_time = CURRENT_TIMESTAMP
        WHERE node_instance_id = #{nodeInstanceId,jdbcType=INTEGER}
    </update>

    <!-- 更新节点实例开始时间 -->
    <update id="updateTaskNodeInstanceStartTime">
        UPDATE public.t_ibdp_dd_task_node_instance
        SET start_time = #{startTime,jdbcType=TIMESTAMP},
            status = 'RUNNING',
            update_time = CURRENT_TIMESTAMP
        WHERE node_instance_id = #{nodeInstanceId,jdbcType=INTEGER}
    </update>

    <!-- 更新节点实例结束时间和耗时 -->
    <update id="updateTaskNodeInstanceEndTime">
        UPDATE public.t_ibdp_dd_task_node_instance
        SET end_time = #{endTime,jdbcType=TIMESTAMP},
            duration_ms = #{durationMs,jdbcType=BIGINT},
            update_time = CURRENT_TIMESTAMP
        WHERE node_instance_id = #{nodeInstanceId,jdbcType=INTEGER}
    </update>

    <!-- 更新节点实例依赖状态 -->
    <update id="updateTaskNodeInstanceDependencyStatus">
        UPDATE public.t_ibdp_dd_task_node_instance
        SET dependency_status = #{dependencyStatus,jdbcType=VARCHAR},
            update_time = CURRENT_TIMESTAMP
        WHERE node_instance_id = #{nodeInstanceId,jdbcType=INTEGER}
    </update>

    <!-- 更新节点实例重试次数 -->
    <update id="updateTaskNodeInstanceRetryCount">
        UPDATE public.t_ibdp_dd_task_node_instance
        SET retry_count = #{retryCount,jdbcType=INTEGER},
            update_time = CURRENT_TIMESTAMP
        WHERE node_instance_id = #{nodeInstanceId,jdbcType=INTEGER}
    </update>

    <!-- 更新节点实例执行信息 -->
    <update id="updateTaskNodeInstanceExecutorInfo">
        UPDATE public.t_ibdp_dd_task_node_instance
        SET job_log_id = #{jobLogId,jdbcType=BIGINT},
            executor_address = #{executorAddress,jdbcType=VARCHAR},
            executor_handler = #{executorHandler,jdbcType=VARCHAR},
            executor_param = #{executorParam,jdbcType=VARCHAR},
            update_time = CURRENT_TIMESTAMP
        WHERE node_instance_id = #{nodeInstanceId,jdbcType=INTEGER}
    </update>

    <!-- 删除节点实例 -->
    <delete id="deleteTaskNodeInstance">
        DELETE FROM public.t_ibdp_dd_task_node_instance
        WHERE node_instance_id = #{nodeInstanceId,jdbcType=INTEGER}
    </delete>

    <!-- 根据工作流实例ID删除所有节点实例 -->
    <delete id="deleteTaskNodeInstancesByWorkflowInstanceId">
        DELETE FROM public.t_ibdp_dd_task_node_instance
        WHERE workflow_instance_id = #{workflowInstanceId,jdbcType=INTEGER}
    </delete>

</mapper>
