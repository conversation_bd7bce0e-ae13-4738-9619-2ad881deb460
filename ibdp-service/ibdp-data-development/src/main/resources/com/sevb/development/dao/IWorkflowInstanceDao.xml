<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sevb.development.dao.IWorkflowInstanceDao">

    <!-- 结果映射 -->
    <resultMap id="workflowInstanceResultMap" type="com.sevb.development.entity.po.WorkflowInstancePO">
        <id column="instance_id" property="instanceId" jdbcType="INTEGER"/>
        <result column="workflow_id" property="workflowId" jdbcType="INTEGER"/>
        <result column="instance_name" property="instanceName" jdbcType="VARCHAR"/>
        <result column="trigger_type" property="triggerType" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="duration_ms" property="durationMs" jdbcType="BIGINT"/>
        <result column="trigger_user" property="triggerUser" jdbcType="VARCHAR"/>
        <result column="trigger_params" property="triggerParams" jdbcType="VARCHAR"/>
        <result column="error_message" property="errorMessage" jdbcType="VARCHAR"/>
        <result column="retry_count" property="retryCount" jdbcType="INTEGER"/>
        <result column="max_retry_count" property="maxRetryCount" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <!-- 关联的工作流名称 -->
        <result column="workflow_name" property="workflowName" jdbcType="VARCHAR"/>
        <!-- 责任人 -->
        <result column="responsible_person" property="responsiblePerson" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 插入工作流实例 -->
    <insert id="insertWorkflowInstance" parameterType="com.sevb.development.entity.po.WorkflowInstancePO"
            useGeneratedKeys="true" keyProperty="instanceId">
        INSERT INTO public.t_ibdp_dd_workflow_instance (
            workflow_id,
            instance_name,
            trigger_type,
            status,
            start_time,
            end_time,
            duration_ms,
            trigger_user,
            trigger_params,
            error_message,
            retry_count,
            max_retry_count,
            create_time,
            update_time
        ) VALUES (
            #{workflowId,jdbcType=INTEGER},
            #{instanceName,jdbcType=VARCHAR},
            #{triggerType,jdbcType=VARCHAR},
            #{status,jdbcType=VARCHAR},
            #{startTime,jdbcType=TIMESTAMP},
            #{endTime,jdbcType=TIMESTAMP},
            #{durationMs,jdbcType=BIGINT},
            #{triggerUser,jdbcType=VARCHAR},
            #{triggerParams,jdbcType=VARCHAR},
            #{errorMessage,jdbcType=VARCHAR},
            #{retryCount,jdbcType=INTEGER},
            #{maxRetryCount,jdbcType=INTEGER},
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
        )
    </insert>

    <!-- 根据实例ID查询工作流实例 -->
    <select id="selectWorkflowInstanceById" resultMap="workflowInstanceResultMap">
        SELECT instance_id, workflow_id, instance_name, trigger_type, status,
               start_time, end_time, duration_ms, trigger_user, trigger_params,
               error_message, retry_count, max_retry_count, create_time, update_time
        FROM public.t_ibdp_dd_workflow_instance
        WHERE instance_id = #{instanceId,jdbcType=INTEGER}
    </select>

    <!-- 根据工作流ID查询工作流实例列表 -->
    <select id="selectWorkflowInstancesByWorkflowId" resultMap="workflowInstanceResultMap">
        SELECT instance_id, workflow_id, instance_name, trigger_type, status,
               start_time, end_time, duration_ms, trigger_user, trigger_params,
               error_message, retry_count, max_retry_count, create_time, update_time
        FROM public.t_ibdp_dd_workflow_instance
        WHERE workflow_id = #{workflowId,jdbcType=INTEGER}
        ORDER BY create_time DESC
    </select>

    <!-- 根据工作流ID查询最新的工作流实例 -->
    <select id="selectLatestWorkflowInstanceByWorkflowId" resultMap="workflowInstanceResultMap">
        SELECT instance_id, workflow_id, instance_name, trigger_type, status,
               start_time, end_time, duration_ms, trigger_user, trigger_params,
               error_message, retry_count, max_retry_count, create_time, update_time
        FROM public.t_ibdp_dd_workflow_instance
        WHERE workflow_id = #{workflowId,jdbcType=INTEGER}
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 根据状态查询工作流实例列表 -->
    <select id="selectWorkflowInstancesByStatus" resultMap="workflowInstanceResultMap">
        SELECT instance_id, workflow_id, instance_name, trigger_type, status,
               start_time, end_time, duration_ms, trigger_user, trigger_params,
               error_message, retry_count, max_retry_count, create_time, update_time
        FROM public.t_ibdp_dd_workflow_instance
        WHERE status = #{status,jdbcType=VARCHAR}
        ORDER BY create_time DESC
    </select>

    <!-- 查询运行中的工作流实例 -->
    <select id="selectRunningWorkflowInstances" resultMap="workflowInstanceResultMap">
        SELECT instance_id, workflow_id, instance_name, trigger_type, status,
               start_time, end_time, duration_ms, trigger_user, trigger_params,
               error_message, retry_count, max_retry_count, create_time, update_time
        FROM public.t_ibdp_dd_workflow_instance
        WHERE status IN ('WAITING', 'RUNNING')
        ORDER BY create_time ASC
    </select>

    <!-- 更新工作流实例状态 -->
    <update id="updateWorkflowInstanceStatus">
        UPDATE public.t_ibdp_dd_workflow_instance
        SET status = #{status,jdbcType=VARCHAR},
            error_message = #{errorMessage,jdbcType=VARCHAR},
            update_time = CURRENT_TIMESTAMP
        WHERE instance_id = #{instanceId,jdbcType=INTEGER}
    </update>

    <!-- 更新工作流实例开始时间 -->
    <update id="updateWorkflowInstanceStartTime">
        UPDATE public.t_ibdp_dd_workflow_instance
        SET start_time = #{startTime,jdbcType=TIMESTAMP},
            status = 'RUNNING',
            update_time = CURRENT_TIMESTAMP
        WHERE instance_id = #{instanceId,jdbcType=INTEGER}
    </update>

    <!-- 更新工作流实例结束时间和耗时 -->
    <update id="updateWorkflowInstanceEndTime">
        UPDATE public.t_ibdp_dd_workflow_instance
        SET end_time = #{endTime,jdbcType=TIMESTAMP},
            duration_ms = #{durationMs,jdbcType=BIGINT},
            update_time = CURRENT_TIMESTAMP
        WHERE instance_id = #{instanceId,jdbcType=INTEGER}
    </update>

    <!-- 更新工作流实例重试次数 -->
    <update id="updateWorkflowInstanceRetryCount">
        UPDATE public.t_ibdp_dd_workflow_instance
        SET retry_count = #{retryCount,jdbcType=INTEGER},
            update_time = CURRENT_TIMESTAMP
        WHERE instance_id = #{instanceId,jdbcType=INTEGER}
    </update>

    <!-- 删除工作流实例 -->
    <delete id="deleteWorkflowInstance">
        DELETE FROM public.t_ibdp_dd_workflow_instance
        WHERE instance_id = #{instanceId,jdbcType=INTEGER}
    </delete>

    <!-- 分页查询工作流实例 -->
    <select id="selectWorkflowInstancesWithPaging" resultMap="workflowInstanceResultMap">
        SELECT wi.instance_id, wi.workflow_id, wi.instance_name, wi.trigger_type, wi.status,
               wi.start_time, wi.end_time, wi.duration_ms, wi.trigger_user, wi.trigger_params,
               wi.error_message, wi.retry_count, wi.max_retry_count, wi.create_time, wi.update_time,
               w.workflow_name, ji.pic as responsible_person
        FROM public.t_ibdp_dd_workflow_instance wi
        LEFT JOIN public.t_ibdp_dd_workflow w ON wi.workflow_id = w.workflow_id
        LEFT JOIN public.t_ibdp_dd_task_node tn ON w.workflow_id = tn.workflow_id
        LEFT JOIN public.job_info ji ON tn.job_id = ji.id
        <where>
            <if test="workflowId != null">
                AND wi.workflow_id = #{workflowId,jdbcType=INTEGER}
            </if>
            <if test="workflowName != null and workflowName != ''">
                AND w.workflow_name LIKE CONCAT('%', #{workflowName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="status != null and status != ''">
                AND wi.status = #{status,jdbcType=VARCHAR}
            </if>
            <if test="startDate != null">
                AND wi.create_time >= #{startDate,jdbcType=TIMESTAMP}
            </if>
            <if test="endDate != null">
                AND wi.create_time &lt;= #{endDate,jdbcType=TIMESTAMP}
            </if>
            <if test="responsiblePerson != null and responsiblePerson != ''">
                AND ji.pic LIKE CONCAT('%', #{responsiblePerson,jdbcType=VARCHAR}, '%')
            </if>
        </where>
        GROUP BY wi.instance_id, wi.workflow_id, wi.instance_name, wi.trigger_type, wi.status,
                 wi.start_time, wi.end_time, wi.duration_ms, wi.trigger_user, wi.trigger_params,
                 wi.error_message, wi.retry_count, wi.max_retry_count, wi.create_time, wi.update_time,
                 w.workflow_name, ji.pic
        ORDER BY wi.create_time DESC
        LIMIT #{limit,jdbcType=INTEGER} OFFSET #{offset,jdbcType=INTEGER}
    </select>

    <!-- 统计工作流实例数量 -->
    <select id="countWorkflowInstances" resultType="int">
        SELECT COUNT(DISTINCT wi.instance_id)
        FROM public.t_ibdp_dd_workflow_instance wi
        LEFT JOIN public.t_ibdp_dd_workflow w ON wi.workflow_id = w.workflow_id
        LEFT JOIN public.t_ibdp_dd_task_node tn ON w.workflow_id = tn.workflow_id
        LEFT JOIN public.job_info ji ON tn.job_id = ji.id
        <where>
            <if test="workflowId != null">
                AND wi.workflow_id = #{workflowId,jdbcType=INTEGER}
            </if>
            <if test="workflowName != null and workflowName != ''">
                AND w.workflow_name LIKE CONCAT('%', #{workflowName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="status != null and status != ''">
                AND wi.status = #{status,jdbcType=VARCHAR}
            </if>
            <if test="startDate != null">
                AND wi.create_time >= #{startDate,jdbcType=TIMESTAMP}
            </if>
            <if test="endDate != null">
                AND wi.create_time &lt;= #{endDate,jdbcType=TIMESTAMP}
            </if>
            <if test="responsiblePerson != null and responsiblePerson != ''">
                AND ji.pic LIKE CONCAT('%', #{responsiblePerson,jdbcType=VARCHAR}, '%')
            </if>
        </where>
    </select>



</mapper>
