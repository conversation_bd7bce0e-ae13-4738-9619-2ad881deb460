<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sevb.development.dao.ITaskNodeConfigDao">


    <!-- 定义 node 表的字段 -->
    <sql id="node_Column_List">
        <!-- Node 相关字段 -->
        node.node_id,
        node.node_name,
        node.node_type,
        node.child_type,
        node.content
    </sql>

    <!-- 定义 config 表的字段 -->
    <sql id="config_Column_List">
        <!-- Config 相关字段 -->
        config.version,
        config.status,
        config.remark,
        config.schedule,
        config.scheduling_method,
        config.scheduling_type,
        config.scheduling_cycle,
        config.scheduling_form,
        config.scheduling_time,
        config.timeout,
        config.log_describe,
        config.created_by,
        config.creation_date,
        config.last_updated_by,
        config.last_update_date,
        config.start_date,
        config.end_date,
        config.is_always_active,
        config.responsible_person,
        config.rerun_attribute,
        config.db_config_id,
        config.jvm_param,
        config.deploy_method
    </sql>

    <!-- 组合字段列表 -->
    <sql id="base_Column_List">
        <!-- 引用 node 和 config 的字段 -->
        <include refid="node_Column_List"/>,
        <include refid="config_Column_List"/>
    </sql>


    <select id="getActiveDataByNodeId" resultType="com.sevb.development.entity.vo.TaskNodeConfigVO">
        SELECT
        <include refid="base_Column_List"/>
        FROM public.t_ibdp_dd_task_node_config config
        left join public.t_ibdp_dd_task_node node ON config.node_id = node.node_id
        WHERE node.node_id = #{nodeId, jdbcType=INTEGER}
        AND config.status = 'Y'
    </select>

    <select id="getTaskNodeConfigByUnique" resultType="com.sevb.development.entity.vo.TaskNodeConfigVO">
        SELECT
        <include refid="base_Column_List"/>
        FROM public.t_ibdp_dd_task_node_config config
        left join public.t_ibdp_dd_task_node node ON config.node_id = node.node_id
        WHERE node.node_id = #{nodeId, jdbcType=INTEGER}
        AND version = #{version, jdbcType=INTEGER}
    </select>

    <insert id="insertTaskNodeConfig" parameterType="com.sevb.development.entity.vo.TaskNodeConfigVO">
        <selectKey resultType="Integer" keyColumn="version" keyProperty="version" order="BEFORE">
            SELECT COALESCE(MAX(VERSION), 0)+1 AS version FROM public.t_ibdp_dd_task_node_config WHERE node_id = #{nodeId, jdbcType=INTEGER}
        </selectKey>
        INSERT INTO public.t_ibdp_dd_task_node_config(
        node_id,
        version,
        status,
        node_name,
        remark,
        schedule,
        scheduling_method,
        scheduling_type,
        scheduling_cycle,
        scheduling_form,
        scheduling_time,
        timeout,
        log_describe,
        created_by,
        creation_date,
        last_updated_by,
        last_update_date,
        start_date,
        end_date,
        is_always_active,
        responsible_person,
        rerun_attribute,
        db_config_id,
        jvm_param,
        deploy_method
        ) VALUES(
        #{nodeId, jdbcType=INTEGER},
        #{version, jdbcType=INTEGER},
        'Y',
        #{nodeName, jdbcType=VARCHAR},
        #{remark, jdbcType=VARCHAR},
        #{schedule, jdbcType=VARCHAR},
        #{schedulingMethod, jdbcType=VARCHAR},
        #{schedulingType, jdbcType=VARCHAR},
        #{schedulingCycle, jdbcType=VARCHAR},
        #{schedulingForm, jdbcType=VARCHAR},
        #{schedulingTime, jdbcType=ARRAY, typeHandler=com.sevb.common.config.StringArrayTypeHandler},
        #{timeout, jdbcType=INTEGER},
        #{logDescribe, jdbcType=VARCHAR},
        #{createdBy, jdbcType=VARCHAR},
        CURRENT_TIMESTAMP,
        #{createdBy, jdbcType=VARCHAR},
        CURRENT_TIMESTAMP,
        #{startDate, jdbcType=TIMESTAMP},
        #{endDate, jdbcType=TIMESTAMP},
        #{isAlwaysActive, jdbcType=BOOLEAN},
        #{responsiblePerson, jdbcType=VARCHAR},
        #{rerunAttribute, jdbcType=VARCHAR},
        #{dbConfigId, jdbcType=VARCHAR},
        #{jvmParam, jdbcType=VARCHAR},
        #{deployMethod, jdbcType=VARCHAR}
        )
    </insert>

    <update id="resetActiveVersion">
        UPDATE public.t_ibdp_dd_task_node_config
        SET status = (CASE version WHEN #{activeVersion, jdbcType=INTEGER} THEN 'Y' ELSE 'N' END)
        WHERE node_id = #{nodeId, jdbcType=INTEGER}
          AND (status = 'Y' OR version = #{activeVersion, jdbcType=INTEGER})
    </update>

    <select id="findListByNodeId" resultType="com.sevb.development.entity.vo.TaskNodeConfigVO">
        SELECT
        <include refid="base_Column_List"/>
        FROM public.t_ibdp_dd_task_node_config config
        left join public.t_ibdp_dd_task_node node ON config.node_id = node.node_id
        WHERE node.node_id = #{nodeId, jdbcType=INTEGER}
        ORDER BY VERSION DESC
    </select>

    <select id="findActiveListByNodeIds" resultType="com.sevb.development.entity.vo.TaskNodeConfigVO">
        SELECT
        <include refid="base_Column_List"/>
        FROM public.t_ibdp_dd_task_node_config config
        left join public.t_ibdp_dd_task_node node ON config.node_id = node.node_id
        WHERE node.node_id IN
        <foreach collection="nodeIds" item="nodeId" open="(" separator="," close=")">
            #{nodeId, jdbcType=INTEGER}
        </foreach>
        AND config.status = 'Y'
    </select>

    <delete id="delTaskNodeConfig">
        DELETE
        FROM public.t_ibdp_dd_task_node_config
        WHERE node_id = #{nodeId, jdbcType=INTEGER}
    </delete>
</mapper>
