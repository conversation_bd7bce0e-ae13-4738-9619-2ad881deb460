<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sevb.development.dao.IWorkflowDao">
    <insert id="insertWorkflowData" parameterType="com.sevb.development.entity.vo.WorkflowVO"
            useGeneratedKeys="true" keyProperty="workflowId">
        INSERT INTO public.t_ibdp_dd_workflow (
        workflow_name,
        remark,
        directory_id,
        create_by,
        create_time,
        update_by,
        update_time
        ) VALUES (
        #{workflowName,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{directoryId,jdbcType=INTEGER},
        #{createBy,jdbcType=VARCHAR},
        LOCALTIMESTAMP,
        #{updateBy,jdbcType=VARCHAR},
        LOCALTIMESTAMP)
    </insert>
    <update id="updateWorkflowData" parameterType="com.sevb.development.entity.vo.WorkflowVO"
            useGeneratedKeys="true" keyProperty="workflowId">
        UPDATE public.t_ibdp_dd_workflow
        SET
        <if test="workflowName != null and workflowName != ''">
            workflow_name = #{workflowName,jdbcType=VARCHAR},
        </if>
        <if test="remark != null and remark != ''">
            remark = #{remark,jdbcType=VARCHAR},
        </if>
        <if test="directoryId != null and directoryId != ''">
            directory_id = #{directoryId,jdbcType=INTEGER},
        </if>
        <if test="schedule != null and schedule != ''">
            schedule = #{schedule,jdbcType=VARCHAR},
        </if>
        <if test="responsiblePerson != null and responsiblePerson != ''">
            responsible_person = #{responsiblePerson,jdbcType=VARCHAR},
        </if>
        <if test="timeout != null and timeout != ''">
            timeout = #{timeout,jdbcType=INTEGER},
        </if>
        <if test="rerunAttribute != null and rerunAttribute != ''">
            rerun_attribute = #{rerunAttribute,jdbcType=VARCHAR},
        </if>
        <if test="isAlwaysActive != null and isAlwaysActive != ''">
            is_always_active = #{isAlwaysActive,jdbcType=BOOLEAN},
        </if>
        <if test="startDate != null and startDate != ''">
            start_date = #{startDate,jdbcType=TIMESTAMP},
        </if>
        <if test="endDate != null and endDate != ''">
            end_date = #{endDate,jdbcType=TIMESTAMP},
        </if>
        <if test="updateBy != null and updateBy != ''">
            update_by = #{updateBy,jdbcType=VARCHAR},
        </if>
        update_time = LOCALTIMESTAMP
        WHERE workflow_id = #{workflowId,jdbcType=INTEGER};
    </update>

    <delete id="deleteWorkflowData">
        DELETE FROM public.t_ibdp_dd_workflow
        WHERE workflow_id = #{workflowId,jdbcType=INTEGER};
    </delete>

    <resultMap id="workflowResultMap" type="com.sevb.development.entity.po.WorkflowPO">
        <id property="workflowId" column="workflow_id"/>
        <result property="workflowName" column="workflow_name"/>
        <result property="remark" column="remark"/>
        <result property="directoryId" column="directory_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="selectWorkflowByDirectoryIds" resultMap="workflowResultMap">
        SELECT workflow_id,
            workflow_name,
            remark,
            directory_id,
            create_by,
            create_time,
            update_by,
            update_time
        FROM public.t_ibdp_dd_workflow
        WHERE directory_id IN
        <foreach collection="list" item="directoryIds" open="(" separator="," close=")">
            #{directoryIds}
        </foreach>
    </select>

    <select id="selectTaskNodesByWorkflowIds" resultType="com.sevb.development.entity.po.TaskNodePO">
        SELECT node_id,
            node_name,
            node_type,
            child_type,
            remark,
            workflow_id,
            create_by,
            create_time,
            update_by,
            update_time
        FROM public.t_ibdp_dd_task_node
        WHERE workflow_id IN
        <foreach collection="list" item="workflowIds" open="(" separator="," close=")">
            #{workflowIds}
        </foreach>
    </select>

    <select id="selectWorkflowByWorkflowId" resultType="com.sevb.development.entity.po.WorkflowPO">
        SELECT workflow_id,
            workflow_name,
            remark,
            directory_id,
            schedule,
            responsible_person,
            timeout,
            rerun_attribute,
            is_always_active,
            start_date,
            end_date,
            create_by,
            create_time,
            update_by,
            update_time
        FROM public.t_ibdp_dd_workflow
        WHERE workflow_id = #{workflowId,jdbcType=INTEGER}
    </select>
</mapper>