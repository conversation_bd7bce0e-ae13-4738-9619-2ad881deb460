<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sevb.datax.admin.mapper.JobTemplateMapper">

	<resultMap id="JobTemplate" type="com.sevb.datax.admin.entity.JobTemplate" >
		<result column="id" property="id" />

		<result column="job_group" property="jobGroup" />
	    <result column="job_cron" property="jobCron" />
	    <result column="job_desc" property="jobDesc" />

	    <result column="add_time" property="addTime" />
	    <result column="update_time" property="updateTime" />

		<result column="user_id" property="userId" />
	    <result column="alarm_email" property="alarmEmail" />

		<result column="executor_route_strategy" property="executorRouteStrategy" />
		<result column="executor_handler" property="executorHandler" />
	    <result column="executor_param" property="executorParam" />
		<result column="executor_block_strategy" property="executorBlockStrategy" />
		<result column="executor_timeout" property="executorTimeout" />
		<result column="executor_fail_retry_count" property="executorFailRetryCount" />

	    <result column="glue_type" property="glueType" />
	    <result column="glue_source" property="glueSource" />
	    <result column="glue_remark" property="glueRemark" />
		<result column="glue_updatetime" property="glueUpdatetime" />

		<result column="child_jobid" property="childJobId" />

		<result column="trigger_last_time" property="triggerLastTime" />
		<result column="trigger_next_time" property="triggerNextTime" />
		<result column="job_json" property="jobJson" />

		<result column="jvm_param" property="jvmParam" />
		<result column="project_id" property="projectId"/>
	</resultMap>

	<sql id="Base_Column_List">
		t.id,
		t.job_group,
		t.job_cron,
		t.job_desc,
		t.add_time,
		t.update_time,
		t.user_id,
		t.alarm_email,
		t.executor_route_strategy,
		t.executor_handler,
		t.executor_param,
		t.executor_block_strategy,
		t.executor_timeout,
		t.executor_fail_retry_count,
		t.glue_type,
		t.glue_source,
		t.glue_remark,
		t.glue_updatetime,
		t.child_jobid,
		t.trigger_last_time,
		t.trigger_next_time,
		t.job_json,
		t.jvm_param,
		t.project_id
	</sql>

	<sql id="Project_Column_List">
		p.name as project_name,
		u.username
	</sql>

	<select id="pageList" parameterType="java.util.HashMap" resultMap="JobTemplate">
		SELECT <include refid="Base_Column_List" />,<include refid="Project_Column_List" />
		FROM job_template AS t
		INNER JOIN job_project p on t.project_id=p.id
		INNER JOIN job_user u on t.user_id = u.id
		<trim prefix="WHERE" prefixOverrides="AND | OR" >
			<if test="jobGroup gt 0">
				AND t.job_group = #{jobGroup}
			</if>
			<if test="jobDesc != null and jobDesc != ''">
				AND t.job_desc like CONCAT(CONCAT('%', #{jobDesc}), '%')
			</if>
			<if test="executorHandler != null and executorHandler != ''">
				AND t.executor_handler like CONCAT(CONCAT('%', #{executorHandler}), '%')
			</if>
			<if test="userId gt 0">
				AND t.user_id = #{userId}
			</if>
			<if test="projectIds != null and projectIds.length != 0">
				AND t.project_id IN
				<foreach collection="projectIds" item="projectId" index="index" open="(" close=")" separator=",">
					#{projectId}
				</foreach>
			</if>
		</trim>
		ORDER BY id DESC
		LIMIT #{offset}, #{pagesize}
	</select>

	<select id="pageListCount" parameterType="java.util.HashMap" resultType="int">
		SELECT count(1)
		FROM job_template AS t
		<trim prefix="WHERE" prefixOverrides="AND | OR" >
			<if test="jobGroup gt 0">
				AND t.job_group = #{jobGroup}
			</if>
			<if test="jobDesc != null and jobDesc != ''">
				AND t.job_desc like CONCAT(CONCAT('%', #{jobDesc}), '%')
			</if>
			<if test="executorHandler != null and executorHandler != ''">
				AND t.executor_handler like CONCAT(CONCAT('%', #{executorHandler}), '%')
			</if>
			<if test="userId gt 0">
				AND t.user_id = #{userId})
			</if>
			<if test="projectIds != null and projectIds.length != 0">
				AND t.project_id IN
				<foreach collection="projectIds" item="projectId" index="index" open="(" close=")" separator=",">
					#{projectId}
				</foreach>
			</if>
		</trim>
	</select>

	<insert id="save" parameterType="com.sevb.datax.admin.entity.JobTemplate" useGeneratedKeys="true" keyProperty="id" >
		INSERT INTO job_template (
			job_group,
			job_cron,
			job_desc,
			add_time,
			update_time,
			user_id,
			alarm_email,
            executor_route_strategy,
			executor_handler,
			executor_param,
			executor_block_strategy,
			executor_timeout,
			executor_fail_retry_count,
			glue_type,
			glue_source,
			glue_remark,
			glue_updatetime,
			child_jobid,
			trigger_last_time,
			trigger_next_time,
			job_json,
			jvm_param,
			project_id
		) VALUES (
			#{jobGroup},
			#{jobCron},
			#{jobDesc},
			#{addTime},
			#{updateTime},
			#{userId},
			#{alarmEmail},
			#{executorRouteStrategy},
			#{executorHandler},
			#{executorParam},
			#{executorBlockStrategy},
			#{executorTimeout},
			#{executorFailRetryCount},
			#{glueType},
			#{glueSource},
			#{glueRemark},
			#{glueUpdatetime},
			#{childJobId},
			#{triggerLastTime},
			#{triggerNextTime},
			#{jobJson},
			#{jvmParam},
			#{projectId}
		);
		<!--<selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
			SELECT LAST_INSERT_ID()
			/*SELECT @@IDENTITY AS id*/
		</selectKey>-->
	</insert>

	<select id="loadById" parameterType="java.util.HashMap" resultMap="JobTemplate">
		SELECT <include refid="Base_Column_List" />
		FROM job_template AS t
		WHERE t.id = #{id}
	</select>

	<update id="update" parameterType="com.sevb.datax.admin.entity.JobTemplate" >
		UPDATE job_template
		SET
			job_group = #{jobGroup},
			job_cron = #{jobCron},
			job_desc = #{jobDesc},
			update_time = #{updateTime},
			user_id = #{userId},
			alarm_email = #{alarmEmail},
			executor_route_strategy = #{executorRouteStrategy},
			executor_handler = #{executorHandler},
			executor_param = #{executorParam},
			executor_block_strategy = #{executorBlockStrategy},
			executor_timeout = ${executorTimeout},
			executor_fail_retry_count = ${executorFailRetryCount},
			glue_type = #{glueType},
			glue_source = #{glueSource},
			glue_remark = #{glueRemark},
			glue_updatetime = #{glueUpdatetime},
			child_jobid = #{childJobId},
			trigger_last_time = #{triggerLastTime},
			trigger_next_time = #{triggerNextTime},
			job_json=#{jobJson},
			jvm_param=#{jvmParam},
			project_id=#{projectId}
		WHERE id = #{id}
	</update>

	<delete id="delete" parameterType="java.util.HashMap">
		DELETE
		FROM job_template
		WHERE id = #{id}
	</delete>

</mapper>