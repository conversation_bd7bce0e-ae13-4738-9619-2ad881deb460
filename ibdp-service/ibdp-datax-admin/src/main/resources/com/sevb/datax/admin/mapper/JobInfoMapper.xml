<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sevb.datax.admin.mapper.JobInfoMapper">

	<resultMap id="JobProject" type="com.sevb.datax.admin.entity.JobInfo">
		<result column="project_id" property="projectId"/>
	</resultMap>
	<resultMap id="JobInfo" type="com.sevb.datax.admin.entity.JobInfo" >
		<result column="id" property="id" />

		<result column="job_group" property="jobGroup" />
	    <result column="job_cron" property="jobCron" />
	    <result column="job_desc" property="jobDesc" />

	    <result column="add_time" property="addTime" />
	    <result column="update_time" property="updateTime" />

	    <result column="user_id" property="userId" />
	    <result column="alarm_email" property="alarmEmail" />

		<result column="executor_route_strategy" property="executorRouteStrategy" />
		<result column="executor_handler" property="executorHandler" />
	    <result column="executor_param" property="executorParam" />
		<result column="executor_block_strategy" property="executorBlockStrategy" />
		<result column="executor_timeout" property="executorTimeout" />
		<result column="executor_fail_retry_count" property="executorFailRetryCount" />

	    <result column="glue_type" property="glueType" />
	    <result column="glue_source" property="glueSource" />
	    <result column="glue_remark" property="glueRemark" />
		<result column="glue_updatetime" property="glueUpdatetime" />

		<result column="child_jobid" property="childJobId" />

		<result column="trigger_status" property="triggerStatus" />
		<result column="trigger_last_time" property="triggerLastTime" />
		<result column="trigger_next_time" property="triggerNextTime" />
		<result column="job_json" property="jobJson" />

		<result column="replace_param" property="replaceParam" />
		<result column="jvm_param" property="jvmParam" />
		<result column="inc_start_time" property="incStartTime" />
		<result column="partition_info" property="partitionInfo" />

		<result column="last_handle_code" property="lastHandleCode" />
		<result column="replace_param_type" property="replaceParamType" />
		<result column="project_id" property="projectId"/>

		<result column="reader_table" property="readerTable" />
		<result column="primary_key" property="primaryKey" />
		<result column="inc_start_id" property="incStartId" />
		<result column="increment_type" property="incrementType" />
		<result column="datasource_id" property="datasourceId" />

		<result column="job_name" property="jobName" />
		<result column="belonging_directory" property="belongingDirectory" />
		<result column="pic" property="pic" />
		<result column="failed_alarm" property="failedAlarm" />
		<result column="tables" property="tables" />
		<result column="category" property="category" />
		<result column="start_time" property="startTime" />
		<result column="value_range" property="valueRange" />
		<result column="data_range_start" property="dataRangeStart" />
		<result column="data_range_end" property="dataRangeEnd" />
		<result column="task_status" property="taskStatus" />
		<result column="cds_id" property="cdsId" />
		<result column="tds_id" property="tdsId" />
		<result column="create_by" property="createBy" />
		<result column="update_by" property="updateBy" />
		<result column="node_id" property="nodeId" />
		<result column="db_config_id" property="dbConfigId" />
	</resultMap>

	<resultMap id="JobInfoVOMap" type="com.sevb.datax.admin.vo.JobInfoVO" >
		<result column="id" property="id" />
		<result column="job_cron" property="jobCron" />
		<result column="job_desc" property="jobDesc" />
		<result column="add_time" property="addTime" />
		<result column="update_time" property="updateTime" />
		<result column="trigger_status" property="triggerStatus" />
		<result column="job_name" property="jobName" />
		<result column="belonging_directory" property="belongingDirectory" />
		<result column="pic" property="pic" />
		<result column="failed_alarm" property="failedAlarm" />
		<result column="tables" property="tables" />
		<result column="category" property="category" />
		<result column="start_time" property="startTime" />
		<result column="value_range" property="valueRange" />
		<result column="data_range_start" property="dataRangeStart" />
		<result column="data_range_end" property="dataRangeEnd" />
		<result column="task_status" property="taskStatus" />
		<result column="cds_id" property="cdsId" />
		<result column="tds_id" property="tdsId" />
		<result column="create_by" property="createBy" />
		<result column="update_by" property="updateBy" />
		<result column="cds_name" property="cdsName" />
		<result column="cds_type" property="cdsType" />
		<result column="cdb_name" property="cdbName" />
		<result column="tds_name" property="tdsName" />
		<result column="tds_type" property="tdsType" />
		<result column="tdb_name" property="tdbName" />
	</resultMap>
	<sql id="Base_Column_List">
		t.id,
		t.job_group,
		t.job_cron,
		t.job_desc,
		t.add_time,
		t.update_time,
		t.user_id,
		t.alarm_email,
		t.executor_route_strategy,
		t.executor_handler,
		t.executor_param,
		t.executor_block_strategy,
		t.executor_timeout,
		t.executor_fail_retry_count,
		t.glue_type,
		t.glue_source,
		t.glue_remark,
		t.glue_updatetime,
		t.child_jobid,
		t.trigger_status,
		t.trigger_last_time,
		t.trigger_next_time,
		t.job_json,
		t.replace_param,
		t.jvm_param,
		t.inc_start_time,
		t.partition_info,
		t.last_handle_code,
		t.replace_param_type,
		t.project_id,
		t.reader_table,
		t.primary_key,
		t.inc_start_id,
		t.increment_type,
		t.datasource_id,

		t.job_name,
		t.belonging_directory,
		t.pic,
		t.failed_alarm,
		t.tables,
		t.category,
		t.start_time,
		t.value_range,
		t.data_range_start,
		t.data_range_end,
		t.task_status,
		t.cds_id,
		t.tds_id,
		t.ds_type,
		t.create_by,
		t.update_by
	</sql>

	<sql id="Project_Column_List">
		p.name as project_name,
		u.username
	</sql>

	<sql id="Node_Colum_List">
		n.node_id,
		n.node_name
	</sql>

	<sql id="DbConfig_Colum_List">
		c.connect_name AS cds_name
	</sql>
	<select id="pageList" parameterType="java.util.HashMap" resultMap="JobInfo">
		SELECT <include refid="Base_Column_List" />,<include refid="Project_Column_List" />,
		<include refid="Node_Colum_List" />,
		<include refid="DbConfig_Colum_List" />
		FROM job_info AS t
		INNER JOIN job_project p on t.project_id=p.id
		INNER JOIN job_user u on t.user_id = u.id
		INNER JOIN t_ibdp_dd_task_node n on t.id = n.job_id
		LEFT JOIN t_ibdp_ds_db_config c on t.cds_id = c.business_id
		<trim prefix="WHERE" prefixOverrides="AND | OR" >
			<if test="jobGroup gt 0">
				AND t.job_group = #{jobGroup}
			</if>
            <if test="triggerStatus gte 0">
                AND t.trigger_status = #{triggerStatus}
            </if>
			<if test="jobDesc != null and jobDesc != ''">
				AND t.job_desc like CONCAT(CONCAT('%', #{jobDesc}), '%')
			</if>
			<if test="glueType != null and glueType != ''">
				AND t.glue_type like CONCAT(CONCAT('%', #{glueType}), '%')
			</if>
			<if test="userId gt 0">
				AND t.user_id = #{userId}
			</if>
            <if test="projectIds != null and projectIds.length != 0">
                AND t.project_id IN
                <foreach collection="projectIds" item="projectId" index="index" open="(" close=")" separator=",">
                    #{projectId}
                </foreach>
            </if>
		    <if test="nodeId != null">
				AND n.node_id = #{nodeId}
			</if>
			<if test="pic != null and pic != ''">
				AND t.pic = #{pic}
			</if>
			<if test="cdsId != null and cdsId != ''">
				AND t.cds_id = #{cdsId}
			</if>
			<if test="dsType != null and dsType != ''">
				<choose>
					<when test="dsType == 'node'">
						AND t.ds_type != 'datax'
					</when>
					<otherwise>
						AND t.ds_type = #{dsType}
					</otherwise>
				</choose>
			</if>
		</trim>
		ORDER BY id DESC
		LIMIT #{pagesize} OFFSET #{offset}
	</select>

	<select id="pageListNew" parameterType="com.sevb.datax.admin.vo.JobInfoParams" resultMap="JobInfoVOMap">
		SELECT
		t.id,
		t.job_cron,
		t.job_desc,
		t.add_time,
		t.update_time,
		t.trigger_status,
		t.job_name,
		t.belonging_directory,
		t.pic,
		t.failed_alarm,
		t.tables,
		t.category,
		t.start_time,
		t.value_range,
		t.data_range_start,
		t.data_range_end,
		t.task_status,
		t.cds_id,
		t.tds_id,
		t.create_by,
		t.update_by,
		db.connect_name cds_name,
		db.config_type cds_type,
		db.segment_02 cdb_name,
		db2.connect_name tds_name,
		db2.config_type tds_type,
		db2.segment_02 tdb_name
		FROM job_info AS t
		LEFT JOIN t_ibdp_ds_db_config as db on db.business_id = t.cds_id
		LEFT JOIN t_ibdp_ds_db_config as db2 on db2.business_id = t.tds_id
		<where>
			t.ds_type = 'datax'
			<if test="jobName != null and jobName != ''">
				AND t.job_name = #{jobName}
			</if>
			<if test="cdsType != null and cdsType != ''">
				AND db.config_type = #{cdsType}
			</if>
			<if test="taskStatus gte 0 and taskStatus != null">
				AND t.task_status = #{taskStatus}
			</if>
			<if test="createBy != null and createBy != ''">
				AND t.create_by = #{createBy}
			</if>
			<if test="startTime != null and startTime != ''">
				<![CDATA[
            AND t.add_time >= TO_DATE(#{startTime},'YYYY-MM-DD HH24:MI:SS')
            ]]>
			</if>
			<if test="endTime != null and endTime != ''">
				<![CDATA[
            AND t.add_time < TO_DATE(#{endTime},'YYYY-MM-DD HH24:MI:SS')
            ]]>
			</if>
		</where>
		ORDER BY id DESC
	</select>


	<select id="findAll" parameterType="java.util.HashMap" resultMap="JobInfo">
		SELECT <include refid="Base_Column_List" />
		FROM job_info AS t
		where t.ds_type = 'datax'
		ORDER BY job_desc ASC
	</select>

	<select id="findByJobNameLike" parameterType="java.util.HashMap" resultMap="JobInfo">
		SELECT <include refid="Base_Column_List" />
		FROM job_info AS t
		WHERE t.job_name LIKE #{jobNamePattern}
		ORDER BY t.id ASC
	</select>

	<select id="pageListCount" parameterType="java.util.HashMap" resultType="int">
		SELECT count(1)
		FROM job_info AS t
		INNER JOIN job_project p on t.project_id=p.id
		INNER JOIN t_ibdp_dd_task_node n on t.id = n.job_id
		INNER JOIN t_ibdp_dd_task_node_config c on c.node_id = n.node_id
		<trim prefix="WHERE" prefixOverrides="AND | OR" >
		        AND c.status = 'Y'
			<if test="jobGroup gt 0">
				AND t.job_group = #{jobGroup}
			</if>
            <if test="triggerStatus gte 0">
                AND t.trigger_status = #{triggerStatus}
            </if>
			<if test="jobDesc != null and jobDesc != ''">
				AND t.job_desc like CONCAT(CONCAT('%', #{jobDesc}), '%')
			</if>
			<if test="glueType != null and glueType != ''">
				AND t.glue_type like CONCAT(CONCAT('%', #{glueType}), '%')
			</if>
			<if test="userId gt 0">
				AND t.user_id =#{userId}
			</if>
            <if test="projectIds != null and projectIds.length != 0">
                AND t.project_id IN
                <foreach collection="projectIds" item="projectId" index="index" open="(" close=")" separator=",">
                    #{projectId}
                </foreach>
            </if>
			<if test="pic != null and pic != ''">
				AND t.pic = #{pic}
			</if>
			<if test="cdsId != null and cdsId != ''">
				AND t.cds_id = #{cdsId}
			</if>
			<if test="dsType != null and dsType != ''">
				<choose>
					<when test="dsType == 'node'">
						AND t.ds_type != 'datax'
					</when>
					<otherwise>
						AND t.ds_type = #{dsType}
					</otherwise>
				</choose>
			</if>
		</trim>
	</select>

	<insert id="save" parameterType="com.sevb.datax.admin.entity.JobInfo" useGeneratedKeys="true" keyProperty="id" >
		INSERT INTO job_info (
			job_group,
			job_cron,
			job_desc,
			add_time,
			update_time,
			user_id,
			alarm_email,
            executor_route_strategy,
			executor_handler,
			executor_param,
			executor_block_strategy,
			executor_timeout,
			executor_fail_retry_count,
			glue_type,
			glue_source,
			glue_remark,
			glue_updatetime,
			child_jobid,
			trigger_status,
			trigger_last_time,
			trigger_next_time,
			job_json,
			replace_param,
			jvm_param,
			inc_start_time,
			partition_info,
			last_handle_code,
		    replace_param_type,
		    project_id,
			reader_table,
			primary_key,
			inc_start_id,
			increment_type,
			datasource_id,

		    job_name,
			belonging_directory,
			pic,
			failed_alarm,
			tables,
			category,
			start_time,
			value_range,
			data_range_start,
			data_range_end,
			task_status,
		    cds_id,
		    tds_id,
			create_by,
			update_by,
		    ds_type
		) VALUES (
			#{jobGroup},
			#{jobCron},
			#{jobDesc},
			#{addTime},
			#{updateTime},
			#{userId},
			#{alarmEmail},
			#{executorRouteStrategy},
			#{executorHandler},
			#{executorParam},
			#{executorBlockStrategy},
			#{executorTimeout},
			#{executorFailRetryCount},
			#{glueType},
			#{glueSource},
			#{glueRemark},
			#{glueUpdatetime},
			#{childJobId},
			#{triggerStatus},
			#{triggerLastTime},
			#{triggerNextTime},
			#{jobJson},
			#{replaceParam},
			#{jvmParam},
			#{incStartTime},
			#{partitionInfo},
			#{lastHandleCode},
			#{replaceParamType},
			#{projectId},
			#{readerTable},
			#{primaryKey},
			#{incStartId},
			#{incrementType},
			#{datasourceId},

			#{jobName},
			#{belongingDirectory},
			#{pic},
			#{failedAlarm},
			#{tables},
			#{category},
			#{startTime},
			#{valueRange},
			#{dataRangeStart},
			#{dataRangeEnd},
			#{taskStatus},
			#{cdsId},
			#{tdsId},
			#{createBy},
			#{updateBy},
		    #{dsType}
		);
		<!--<selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
			SELECT LAST_INSERT_ID()
			/*SELECT @@IDENTITY AS id*/
		</selectKey>-->
	</insert>

	<select id="loadById" parameterType="java.util.HashMap" resultMap="JobInfo">
		SELECT <include refid="Base_Column_List" />
		FROM job_info AS t
		WHERE t.id = #{id}
	</select>

	<update id="update" parameterType="com.sevb.datax.admin.entity.JobInfo" >
        UPDATE job_info
        <set>
            <if test="jobGroup != null and jobGroup != ''">
                job_group = #{jobGroup},
            </if>
            <if test="jobCron != null and jobCron != ''">
                job_cron = #{jobCron},
            </if>
            <if test="jobDesc != null and jobDesc != ''">
                job_desc = #{jobDesc},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="alarmEmail != null and alarmEmail != ''">
                alarm_email = #{alarmEmail},
            </if>
            <if test="executorRouteStrategy != null and executorRouteStrategy != ''">
                executor_route_strategy = #{executorRouteStrategy},
            </if>
            <if test="executorHandler != null and executorHandler != ''">
                executor_handler = #{executorHandler},
            </if>
            <if test="executorParam != null and executorParam != ''">
                executor_param = #{executorParam},
            </if>
            <if test="executorBlockStrategy != null and executorBlockStrategy != ''">
                executor_block_strategy = #{executorBlockStrategy},
            </if>
            <if test="executorTimeout != null and executorTimeout != ''">
                executor_timeout = #{executorTimeout},
            </if>
            <if test="executorFailRetryCount != null and executorFailRetryCount != ''">
                executor_fail_retry_count = #{executorFailRetryCount},
            </if>
            <if test="glueType != null and glueType != ''">
                glue_type = #{glueType},
            </if>
            <if test="glueSource != null and glueSource != ''">
                glue_source = #{glueSource},
            </if>
            <if test="glueRemark != null and glueRemark != ''">
                glue_remark = #{glueRemark},
            </if>
            <if test="glueUpdatetime != null">
                glue_updatetime = #{glueUpdatetime},
            </if>
            <if test="childJobId != null and childJobId != ''">
                child_jobid = #{childJobId},
            </if>
            <if test="triggerStatus != null and triggerStatus != ''">
                trigger_status = #{triggerStatus},
            </if>
            <if test="triggerLastTime != null">
                trigger_last_time = #{triggerLastTime},
            </if>
            <if test="triggerNextTime != null">
                trigger_next_time = #{triggerNextTime},
            </if>
            <if test="jobJson != null and jobJson != ''">
                job_json = #{jobJson},
            </if>
            <if test="replaceParam != null and replaceParam != ''">
                replace_param = #{replaceParam},
            </if>
            <if test="jvmParam != null and jvmParam != ''">
                jvm_param = #{jvmParam},
            </if>
            <if test="incStartTime != null">
                inc_start_time = #{incStartTime},
            </if>
            <if test="partitionInfo != null and partitionInfo != ''">
                partition_info = #{partitionInfo},
            </if>
            <if test="lastHandleCode != null and lastHandleCode != ''">
                last_handle_code = #{lastHandleCode},
            </if>
            <if test="replaceParamType != null and replaceParamType != ''">
                replace_param_type = #{replaceParamType},
            </if>
            <if test="projectId != null and projectId != ''">
                project_id = #{projectId},
            </if>
            <if test="readerTable != null and readerTable != ''">
                reader_table = #{readerTable},
            </if>
            <if test="primaryKey != null and primaryKey != ''">
                primary_key = #{primaryKey},
            </if>
            <if test="incStartId != null and incStartId != ''">
                inc_start_id = #{incStartId},
            </if>
            <if test="incrementType != null">
                increment_type = #{incrementType},
            </if>
            <if test="datasourceId != null and datasourceId != ''">
                datasource_id = #{datasourceId},
            </if>
            <if test="jobName != null and jobName != ''">
                job_name = #{jobName},
            </if>
            <if test="belongingDirectory != null and belongingDirectory != ''">
                belonging_directory = #{belongingDirectory},
            </if>
            <if test="pic != null and pic != ''">
                pic = #{pic},
            </if>
            <if test="failedAlarm != null and failedAlarm != ''">
                failed_alarm = #{failedAlarm},
            </if>
            <if test="tables != null and tables != ''">
                tables = #{tables},
            </if>
            <if test="category != null and category != ''">
                category = #{category},
            </if>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
            <if test="valueRange != null and valueRange != ''">
                value_range = #{valueRange},
            </if>
            <if test="dataRangeStart != null">
                data_range_start = #{dataRangeStart},
            </if>
            <if test="dataRangeEnd != null">
                data_range_end = #{dataRangeEnd},
            </if>
            <if test="tdsId != null and tdsId != ''">
                tds_id = #{tdsId},
            </if>
            <if test="cdsId != null and cdsId != ''">
                cds_id = #{cdsId},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy}
            </if>
        </set>
        WHERE id = #{id}
    </update>

	<delete id="delete" parameterType="java.util.HashMap">
		DELETE
		FROM job_info
		WHERE id = #{id}
	</delete>

	<select id="getJobsByGroup" parameterType="java.util.HashMap" resultMap="JobInfo">
		SELECT <include refid="Base_Column_List" />
		FROM job_info AS t
		WHERE t.job_group = #{jobGroup}
	</select>

	<select id="findAllCount" resultType="int">
		SELECT count(1)
		FROM job_info
	</select>


	<select id="scheduleJobQuery" parameterType="java.util.HashMap" resultMap="JobInfo">
		SELECT <include refid="Base_Column_List" />
		FROM job_info AS t
		WHERE t.trigger_status = 1
			and t.trigger_next_time <![CDATA[ <= ]]> #{maxNextTime}
			and t.start_time <![CDATA[ <= ]]> now()
		ORDER BY id ASC
		LIMIT #{pagesize}
	</select>

	<update id="scheduleUpdate" parameterType="com.sevb.datax.admin.entity.JobInfo"  >
		UPDATE job_info
		SET
			trigger_last_time = #{triggerLastTime},
			trigger_next_time = #{triggerNextTime},
			trigger_status = #{triggerStatus}
		WHERE id = #{id}
	</update>

	<update id="incrementTimeUpdate" parameterType="java.util.HashMap">
		UPDATE job_info
		SET
			inc_start_time = #{incStartTime}
		WHERE id = #{id}
	</update>

	<update id="updateLastHandleCode" parameterType="java.util.HashMap">
		UPDATE job_info
		SET
			last_handle_code = #{lastHandleCode},
			task_status = CASE
				WHEN #{lastHandleCode} = 100 THEN 1
				WHEN #{lastHandleCode} = 200 THEN 2
				WHEN #{lastHandleCode} = 500 THEN 3
				ELSE 4
			END
		WHERE id = #{id}
	</update>

	<update id="incrementIdUpdate" parameterType="java.util.HashMap">
		UPDATE job_info
		SET
		inc_start_id = #{incStartId}
		WHERE id = #{id}
	</update>
</mapper>