<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sevb.datax.admin.mapper.JobGroupMapper">

    <resultMap id="JobGroup" type="com.sevb.datax.admin.entity.JobGroup">
        <result column="id" property="id"/>
        <result column="app_name" property="appName"/>
        <result column="title" property="title"/>
        <result column="order" property="order"/>
        <result column="address_type" property="addressType"/>
        <result column="address_list" property="addressList"/>
    </resultMap>

    <sql id="Base_Column_List">
		t.id,
		t.app_name,
		t.title,
		t.order,
		t.address_type,
		t.address_list
	</sql>

    <select id="findAll" resultMap="JobGroup">
        SELECT
        <include refid="Base_Column_List"/>
        FROM job_group AS t
        ORDER BY t.order ASC
    </select>

    <select id="find" resultMap="JobGroup">
        select
        id,app_name,title,"order",address_type,address_list
        from job_group
        <where>
            <if test="appName != null and appName != ''">
                and app_name like concat('%',#{appName,jdbcType=VARCHAR},'%')
            </if>
            <if test="title != null and title != ''">
                and title like concat('%',#{title,jdbcType=VARCHAR},'%')
            </if>
            <if test="addressList != null and addressList != ''">
                and address_list like concat('%',#{addressList,jdbcType=VARCHAR},'%')
            </if>
        </where>
    </select>

    <select id="findByAddressType" parameterType="java.lang.Integer" resultMap="JobGroup">
        SELECT
        <include refid="Base_Column_List"/>
        FROM job_group AS t
        WHERE t.address_type = #{addressType}
        ORDER BY t.order ASC
    </select>

    <insert id="save" parameterType="com.sevb.datax.admin.entity.JobGroup" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO job_group ( app_name, title, "order", address_type, address_list)
		values ( #{appName}, #{title}, #{order}, #{addressType}, #{addressList});
	</insert>

    <update id="update" parameterType="com.sevb.datax.admin.entity.JobGroup">
		UPDATE job_group
		SET app_name = #{appName},
			title = #{title},
			"order" = #{order},
			address_type = #{addressType},
			address_list = #{addressList}
		WHERE id = #{id}
	</update>

    <delete id="remove" parameterType="java.lang.Integer">
		DELETE FROM job_group
		WHERE id = #{id}
	</delete>

    <select id="load" parameterType="java.lang.Integer" resultMap="JobGroup">
        SELECT
        <include refid="Base_Column_List"/>
        FROM job_group AS t
        WHERE t.id = #{id}
    </select>

</mapper>