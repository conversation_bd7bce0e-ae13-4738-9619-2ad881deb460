package com.sevb.datax.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description: TODO
 * @Author: Creators
 * @Date: 2025年04月17日 10:28:20
 */
@Getter
@Setter
@ApiModel(description = "任务查询参数")
public class JobInfoParams {
    @ApiModelProperty(value = "创建时间开始")
    private String startTime;
    @ApiModelProperty(value = "创建时间结束")
    private String endTime;
    @ApiModelProperty(value = "创建人")
    private String createBy;
    @ApiModelProperty(value = "数据源类型")
    private String cdsType;
    @ApiModelProperty(value = "任务状态")
    private Integer taskStatus;
    @ApiModelProperty(value = "任务名称")
    private String jobName;
    @ApiModelProperty(value = "责任人")
    private String pic;
    @ApiModelProperty(value = "数据源id")
    private String cdsId;
    @ApiModelProperty(value = "数据来源类型")
    private String dsType;
}
