package com.sevb.datax.admin.mapper;

import com.sevb.datax.admin.entity.JobInfo;
import com.sevb.datax.admin.vo.JobInfoParams;
import com.sevb.datax.admin.vo.JobInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;


/**
 * job info
 *
 * <AUTHOR> 2016-1-12 18:03:45
 */
@Mapper
public interface JobInfoMapper {

    List<JobInfoVO> pageListNew(JobInfoParams jobInfoParams);


    List<JobInfo> pageList(@Param("offset") int offset,
                           @Param("pagesize") int pagesize,
                           @Param("jobGroup") int jobGroup,
                           @Param("triggerStatus") int triggerStatus,
                           @Param("jobDesc") String jobDesc,
                           @Param("glueType") String glueType,
                           @Param("userId") int userId,
                           @Param("projectIds") Integer[] projectIds,
                           @Param("nodeId") Integer nodeId,
                           @Param("pic") String pic,
                           @Param("cdsId") String tdsId,
                           @Param("dsType") String dsType);

    int pageListCount(@Param("offset") int offset,
                      @Param("pagesize") int pagesize,
                      @Param("jobGroup") int jobGroup,
                      @Param("triggerStatus") int triggerStatus,
                      @Param("jobDesc") String jobDesc,
                      @Param("glueType") String glueType,
                      @Param("userId") int userId,
                      @Param("projectIds") Integer[] projectIds,
                      @Param("nodeId") Integer nodeId,
                      @Param("pic") String pic,
                      @Param("cdsId") String tdsId,
                      @Param("dsType") String dsType);

    List<JobInfo> findAll();

    List<JobInfo> findByJobNameLike(@Param("jobNamePattern") String jobNamePattern);

    int save(JobInfo info);

    JobInfo loadById(@Param("id") int id);

    int update(JobInfo jobInfo);

    int delete(@Param("id") long id);

    List<JobInfo> getJobsByGroup(@Param("jobGroup") int jobGroup);

    int findAllCount();

    List<JobInfo> scheduleJobQuery(@Param("maxNextTime") long maxNextTime, @Param("pagesize") int pagesize);

    int scheduleUpdate(JobInfo xxlJobInfo);

    int incrementTimeUpdate(@Param("id") int id, @Param("incStartTime") Date incStartTime);

	public int updateLastHandleCode(@Param("id") int id,@Param("lastHandleCode")int lastHandleCode);

    void incrementIdUpdate(@Param("id") int id, @Param("incStartId")Long incStartId);
}
