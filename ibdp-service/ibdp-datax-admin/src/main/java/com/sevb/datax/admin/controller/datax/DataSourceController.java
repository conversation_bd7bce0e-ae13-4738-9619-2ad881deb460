package com.sevb.datax.admin.controller.datax;

import com.sevb.common.util.JasyptUtil;
import com.sevb.common.util.RequestReturnVO;
import com.sevb.datax.admin.dto.DataSourceDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: TODO
 * @Author: Creators
 * @Date: 2025年03月17日 17:44:43
 */

@Api(tags = "数据源相关信息接口")
@RestController()
@RequestMapping("/ds")
public class DataSourceController {

    @Value("${jasypt.encryptor.password}")
    private String jasyptPassword;

    // @ApiOperation(value = "获取库名")
    // @PostMapping("/getDbOrSchema")
    // public RequestReturnVO getOrSchema(@RequestBody DataSourceDTO dto) {
    //     try {
    //         String connectionUrl = getConnectionUrl(dto);
    //         if (connectionUrl == null) {
    //             return RequestReturnVO.fail("数据库信息异常");
    //         }
    //         Class.forName(driver(dto.getType()));
    //         Connection connection = DriverManager.getConnection(connectionUrl, dto.getDbUserName(), dto.getDbPassword());
    //         DatabaseMetaData metaData = connection.getMetaData();
    //         List<String> databaseNames = dbName(dto.getType(), metaData);
    //         // 查询所有非系统数据库
    //         connection.close();
    //         return RequestReturnVO.success(databaseNames);
    //     } catch (ClassNotFoundException | SQLException e) {
    //         return RequestReturnVO.fail("数据库连接失败", e.getMessage());
    //     }
    // }


    @ApiOperation(value = "获取数据库表名")
    @PostMapping("/getTableNames")
    public RequestReturnVO getTableNames(@RequestBody DataSourceDTO dto) {
        try {

            Class.forName(driver(dto.getType()));
            String pwd = JasyptUtil.decyptPwd(jasyptPassword,dto.getDbPassword());
            Connection connection = DriverManager.getConnection(dto.getDbUrl()+"?serverTimezone=UTC", dto.getDbUserName(), pwd);
            DatabaseMetaData metaData = connection.getMetaData();
            ResultSet tables = metaData.getTables(dto.getDbName(), null, "%", new String[]{"TABLE"});
            List<String> tableNames = new ArrayList<>();
            while (tables.next()) {
                String tableName = tables.getString("TABLE_NAME");
                tableNames.add(tableName);
            }
            tables.close();
            connection.close();
            return RequestReturnVO.success(tableNames);
        } catch (ClassNotFoundException | SQLException e) {
            return RequestReturnVO.fail("数据库连接失败", e.getMessage());
        }
    }
    @ApiOperation(value = "获取数据库表的字段信息")
    @PostMapping("/getColumnNames")
    public RequestReturnVO getColumnNames(@RequestBody DataSourceDTO dto) {
        try {

            Class.forName(driver(dto.getType()));
            String pwd = JasyptUtil.decyptPwd(jasyptPassword,dto.getDbPassword());
            Connection connection = DriverManager.getConnection(dto.getDbUrl()+"?serverTimezone=UTC", dto.getDbUserName(), pwd);
            DatabaseMetaData metaData = connection.getMetaData();
            ResultSet resultSet = metaData.getColumns(null, null, dto.getTableName(), "%");
            List<String> tableNames = new ArrayList<>();
            Map<String,String> columnInfo = new HashMap<>();;
            while (resultSet.next()) {
                // 字段名
                String columnName = resultSet.getString("COLUMN_NAME");
                // 字段类型
                String columnType = resultSet.getString("TYPE_NAME");
                // 字段长度
                int columnSize = resultSet.getInt("COLUMN_SIZE");
                // 是否允许为空
                boolean isNullable = resultSet.getBoolean("NULLABLE");
                columnInfo.put(columnName,columnType);
            }
            resultSet.close();
            connection.close();
            return RequestReturnVO.success(columnInfo);
        } catch (ClassNotFoundException | SQLException e) {
            return RequestReturnVO.fail("数据库连接失败", e.getMessage());
        }
    }
    private List<String> dbName(String type,DatabaseMetaData metaData) throws SQLException {
        List<String> databaseNames = new ArrayList<>();
        ResultSet databases;
        switch (type) {
            case "MySQL":
            case "Doris":
                databases = metaData.getCatalogs();
                while (databases.next()) {
                    String dbName = databases.getString("TABLE_CAT");
                    // 过滤掉系统库
                    if (!"information_schema".equals(dbName) && !"mysql".equals(dbName) && !"performance_schema".equals(dbName) && !"sys".equals(dbName)) {
                        databaseNames.add(dbName);
                    }
                }
                databases.close();
                return databaseNames;
            case "Oracle":
                databases = metaData.getSchemas();
                while (databases.next()) {
                    String schemaName = databases.getString("TABLE_SCHEM");
                    // 过滤掉系统架构
                    if (!schemaName.startsWith("SYS") && !"SYSTEM".equals(schemaName)) {
                        databaseNames.add(schemaName);
                    }
                }
                databases.close();
                return databaseNames;
            case "PostgreSql":
                databases = metaData.getCatalogs();
                System.out.println("PostgreSQL 中的非系统库名：");
                while (databases.next()) {
                    String dbName = databases.getString("TABLE_CAT");
                    // 过滤掉系统库
                    if (!"information_schema".equals(dbName) && !dbName.startsWith("pg_")) {
                        databaseNames.add(dbName);
                    }
                }
                databases.close();
                return databaseNames;
            case "Hive":
                databases = metaData.getCatalogs();
                while (databases.next()) {
                    String dbName = databases.getString("TABLE_CAT");
                    // 过滤掉系统库
                    if (!"default".equals(dbName) && !dbName.startsWith("system") && !dbName.startsWith("hive")) {
                        databaseNames.add(dbName);
                    }
                }
                databases.close();
                return databaseNames;
            case "TDengine":
                databases = metaData.getCatalogs();
                while (databases.next()) {
                    String dbName = databases.getString("db_name");
                    // 过滤掉系统库
                    if (!"information_schema".equals(dbName) && !dbName.startsWith("system") && !dbName.startsWith("tdengine")) {
                        databaseNames.add(dbName);
                    }
                }
                databases.close();
                return databaseNames;
            default:
                return null;
        }
    }
    private String schema(String type) {
        switch (type) {
            case "MySQL":
            case "Doris":
                return "SELECT schema_name AS DatabaseName FROM information_schema.schemata WHERE schema_name NOT IN ('information_schema', 'mysql', 'performance_schema', 'sys');";
            case "Oracle":
                return "SELECT DISTINCT owner FROM all_objects WHERE owner = USER;";
            case "PostgreSql":
                return "SELECT datname FROM pg_database WHERE datistemplate = false;";
            case "Hive":
                return "SELECT schema_name AS DatabaseName FROM information_schema.schemata WHERE schema_name NOT IN ('information_schema', 'mysql', 'performance_schema', 'sys');";
            case "TDengine":
                return "SELECT schema_name AS DatabaseName FROM information_schema.schemata WHERE schema_name NOT IN ('information_schema', 'mysql', 'performance_schema', 'sys');";
            default:
                return null;
        }
    }

    private String driver(String type) {
        switch (type) {
            case "MySQL":
            case "Doris":
                return "com.mysql.cj.jdbc.Driver";
            case "Oracle":
                return "oracle.jdbc.driver.OracleDriver";
            case "PostgreSql":
                return "org.postgresql.Driver";
            case "Hive":
                return "org.apache.hive.jdbc.HiveDriver";
            case "TDengine":
                return "com.taosdata.jdbc.TSDBDriver";
            default:
                return null;
        }
    }

    private String getConnectionUrl(DataSourceDTO dto) {
        switch (dto.getType()) {
            case "MySQL":
            case "Doris":
                return "jdbc:mysql://" + dto.getDbUrl() + ":" + dto.getDbPort() + "/" + dto.getDbName() + "?serverTimezone=UTC";
            case "Oracle":
                return "jdbc:oracle:thin:@" + dto.getDbUrl() + ":" + dto.getDbPort() + ":" + dto.getDbName();
            case "PostgreSql":
                return "jdbc:postgresql://" + dto.getDbUrl() + ":" + dto.getDbPort() + "/" + dto.getDbName();
            case "Hive":
                return "jdbc:hive2://" + dto.getDbUrl() + ":" + dto.getDbPort() + "/" + dto.getDbName();
            case "TDengine":
                return "jdbc:taos:://" + dto.getDbUrl() + ":" + dto.getDbPort() + "/" + dto.getDbName();
            default:
                return null;
        }
    }

}
