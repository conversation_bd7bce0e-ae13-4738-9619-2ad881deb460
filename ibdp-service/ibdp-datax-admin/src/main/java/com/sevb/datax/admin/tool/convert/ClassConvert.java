package com.sevb.datax.admin.tool.convert;

import cn.hutool.core.collection.CollectionUtil;
import com.sevb.common.enums.DsType;
import com.sevb.datax.admin.dto.DataXJsonBuildDtoCp;
import com.sevb.datax.admin.dto.RdbmsReaderDto;
import com.sevb.datax.admin.dto.RdbmsWriterDto;
import com.sevb.datax.admin.entity.JobInfo;
import com.sevb.datax.admin.vo.JobInfoVO;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.InvocationTargetException;
import java.util.Collections;

/**
 * @Description: TODO
 * @Author: Creators
 * @Date: 2025年04月17日 11:47:34
 */
public class ClassConvert {

    public static JobInfo voConvertInfo(JobInfoVO vo) throws InvocationTargetException, IllegalAccessException {
        JobInfo jobInfo = new JobInfo();
        BeanUtils.copyProperties(jobInfo,vo);
        jobInfo.setUserId(1);
        //阻塞策略
        jobInfo.setExecutorBlockStrategy("SERIAL_EXECUTION");
        //执行器
        jobInfo.setExecutorHandler("executorJobHandler");
        //路由策略
        jobInfo.setExecutorRouteStrategy("ROUND");
        //项目归属
        jobInfo.setProjectId(1);
        //任务分区
        jobInfo.setJobGroup(1);
        //任务类别
        jobInfo.setGlueType("BEAN");
        //任务重试次数
        jobInfo.setExecutorFailRetryCount(0);
        //超时时间
        jobInfo.setExecutorTimeout(30);
        //任务状态
        jobInfo.setTaskStatus(0);
        jobInfo.setTriggerStatus(0);
        jobInfo.setTriggerLastTime(0);
        jobInfo.setTriggerNextTime(0);
        jobInfo.setCreateBy("admin");
        jobInfo.setUpdateBy("admin");
        jobInfo.setDsType(DsType.DATAX.getValue());
        return jobInfo;
    }

    public static DataXJsonBuildDtoCp builderJsonParams(JobInfoVO jobInfo){
        DataXJsonBuildDtoCp dto = new DataXJsonBuildDtoCp();
        RdbmsReaderDto readerDto = new RdbmsReaderDto();
        readerDto.setReaderSplitPk("");
        readerDto.setWhereParams("");
        readerDto.setQuerySql("");
        dto.setRdbmsReader(readerDto);
        RdbmsWriterDto writerDto = new RdbmsWriterDto();
        writerDto.setPostSql("");
        // 默认是全量同步
        writerDto.setPreSql("truncate table " + jobInfo.getTables());
        dto.setRdbmsWriter(writerDto);
        dto.setReaderDatasourceId(jobInfo.getCdsId());
        dto.setWriterDatasourceId(jobInfo.getTdsId());
        dto.setReaderTables(Collections.singletonList(jobInfo.getTables()));
        dto.setWriterTables(Collections.singletonList(jobInfo.getTables()));
        if (CollectionUtil.isNotEmpty(jobInfo.getReaderColumns()) && CollectionUtil.isNotEmpty(jobInfo.getWriterColumns())){
            dto.setReaderColumns(jobInfo.getReaderColumns());
            dto.setWriterColumns(jobInfo.getWriterColumns());
        }
        return dto;
    }
}
