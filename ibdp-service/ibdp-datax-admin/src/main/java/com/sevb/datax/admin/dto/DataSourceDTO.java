package com.sevb.datax.admin.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description: TODO
 * @Author: Creators
 * @Date: 2025年03月11日 15:35:17
 */
@Getter
@Setter
@ApiModel(description = "数据源信息")
public class DataSourceDTO {
    @ApiModelProperty(value = "数据源id")
    private String id;
    @ApiModelProperty(value = "数据源名称")
    private String name;
    @ApiModelProperty(value = "数据源类型")
    private String type;
    @ApiModelProperty(value = "数据库连接ip")
    private String dbUrl;
    @ApiModelProperty(value = "用户名称")
    private String dbUserName;
    @ApiModelProperty(value = "密码")
    private String dbPassword;
    @ApiModelProperty(value = "数据库名称")
    private String dbName;
    @ApiModelProperty(value = "端口")
    private String dbPort;

    @ApiModelProperty(value = "表名")
    private String tableName;

}
