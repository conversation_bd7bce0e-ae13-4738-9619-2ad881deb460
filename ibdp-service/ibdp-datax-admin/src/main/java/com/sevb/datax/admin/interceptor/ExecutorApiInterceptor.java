package com.sevb.datax.admin.interceptor;

import com.alibaba.fastjson.JSON;
import com.sevb.common.util.RequestReturnVO;
import com.sevb.datax.admin.core.conf.JobAdminConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * 执行器API认证拦截器
 * 验证执行器的访问权限和令牌
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Slf4j
@Component
public class ExecutorApiInterceptor implements HandlerInterceptor {

    private static final String ACCESS_TOKEN_HEADER = "X-Access-Token";
    private static final String EXECUTOR_ADDRESS_HEADER = "X-Executor-Address";
    private static final String API_PREFIX = "/api/v1/executor";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String requestURI = request.getRequestURI();
        
        // 只拦截执行器API请求
        if (!requestURI.startsWith(API_PREFIX)) {
            return true;
        }

        // 健康检查接口不需要认证
        if (requestURI.endsWith("/health") || requestURI.endsWith("/version")) {
            return true;
        }

        try {
            // 验证访问令牌
            if (!validateAccessToken(request)) {
                writeErrorResponse(response, "EXECUTOR_AUTH_001", "访问令牌验证失败");
                return false;
            }

            // 验证执行器地址
            if (!validateExecutorAddress(request)) {
                writeErrorResponse(response, "EXECUTOR_AUTH_002", "执行器地址验证失败");
                return false;
            }

            // 记录访问日志
            logApiAccess(request);
            
            return true;
        } catch (Exception e) {
            log.error("执行器API认证异常", e);
            writeErrorResponse(response, "EXECUTOR_AUTH_003", "认证服务异常");
            return false;
        }
    }

    /**
     * 验证访问令牌
     */
    private boolean validateAccessToken(HttpServletRequest request) {
        String accessToken = request.getHeader(ACCESS_TOKEN_HEADER);
        String configuredToken = JobAdminConfig.getAdminConfig().getAccessToken();
        
        // 如果配置了访问令牌，则必须验证
        if (StringUtils.hasText(configuredToken)) {
            if (!configuredToken.equals(accessToken)) {
                log.warn("访问令牌验证失败，请求令牌: {}, 配置令牌: {}", accessToken, configuredToken);
                return false;
            }
        }
        
        return true;
    }

    /**
     * 验证执行器地址
     */
    private boolean validateExecutorAddress(HttpServletRequest request) {
        String executorAddress = request.getHeader(EXECUTOR_ADDRESS_HEADER);
        
        // 执行器地址不能为空
        if (!StringUtils.hasText(executorAddress)) {
            log.warn("执行器地址为空");
            return false;
        }

        // 简单的地址格式验证
        if (!executorAddress.startsWith("http://") && !executorAddress.startsWith("https://")) {
            log.warn("执行器地址格式不正确: {}", executorAddress);
            return false;
        }

        return true;
    }

    /**
     * 记录API访问日志
     */
    private void logApiAccess(HttpServletRequest request) {
        String executorAddress = request.getHeader(EXECUTOR_ADDRESS_HEADER);
        String requestURI = request.getRequestURI();
        String method = request.getMethod();
        String remoteAddr = getClientIpAddress(request);
        
        log.info("执行器API访问: {} {} from {} (executor: {})", 
                method, requestURI, remoteAddr, executorAddress);
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StringUtils.hasText(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (StringUtils.hasText(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    /**
     * 写入错误响应
     */
    private void writeErrorResponse(HttpServletResponse response, String errorCode, String errorMessage) throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");
        
        RequestReturnVO errorResponse = RequestReturnVO.fail(errorCode, errorMessage);
        String jsonResponse = JSON.toJSONString(errorResponse);
        
        try (PrintWriter writer = response.getWriter()) {
            writer.write(jsonResponse);
            writer.flush();
        }
    }
}
