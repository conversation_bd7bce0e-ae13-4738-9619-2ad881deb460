package com.sevb.datax.admin.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * xxl-job info
 *
 * <AUTHOR>  2019-11-17 14:25:49
 */
@Data
public class JobInfoVO {

	@ApiModelProperty("主键ID")
	private int id;
	@ApiModelProperty("任务执行CRON表达式（执行计划）")
	private String jobCron;
	@ApiModelProperty("创建时间")
	private Date addTime;
	@ApiModelProperty("更新时间")
	private Date updateTime;
	@ApiModelProperty("调度状态：0-下线，1-上线")
	private int triggerStatus;
	@ApiModelProperty("任务名称")
	private String jobName;
	@ApiModelProperty("任务描述")
	private String jobDesc;
	@ApiModelProperty("采集后归属目录")
	private String belongingDirectory;
	@ApiModelProperty("负责人")
	private String pic;
	@ApiModelProperty("采集失败告警")
	private String failedAlarm;
	@ApiModelProperty("属性（数据采集的表）")
	private String tables;
	@ApiModelProperty("规则配置类别（周期取数，手动采集）")
	private String category;
	@ApiModelProperty("任务开始时间")
	private Date startTime;
	@ApiModelProperty("数据范围（手动采集）")
	private String valueRange;
	@ApiModelProperty("数据范围-开始时间")
	private Date dataRangeStart;
	@ApiModelProperty("数据范围-截止时间")
	private Date dataRangeEnd;
	@ApiModelProperty("任务状态(0-未开始, 1-进行中,2- 成功,3- 失败,4- 告警)")
	private int taskStatus;
	@ApiModelProperty("采集数据源id")
	private String cdsId;
	@ApiModelProperty("目标数据源id")
	private String tdsId;
	@ApiModelProperty("创建人")
	private String createBy;
	@ApiModelProperty("更新人")
	private String updateBy;

	@ApiModelProperty("采集数据源类型")
	private String cdsType;
	@ApiModelProperty("采集数据源名称")
	private String cdsName;
	@ApiModelProperty("采集库/Schema")
	private String cdbName;

	@ApiModelProperty("目标数据源类型")
	private String tdsType;
	@ApiModelProperty("目标数据源名称")
	private String tdsName;
	@ApiModelProperty("目标库/Schema")
	private String tdbName;

	@ApiModelProperty("原表字段")
	private List<String> readerColumns;
	@ApiModelProperty("目标表字段")
	private List<String> writerColumns;

}
