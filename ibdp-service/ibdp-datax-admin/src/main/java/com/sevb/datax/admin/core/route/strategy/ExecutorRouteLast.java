package com.sevb.datax.admin.core.route.strategy;

import com.sevb.datax.admin.core.route.ExecutorRouter;
import com.wugui.datatx.core.biz.model.ReturnT;
import com.wugui.datatx.core.biz.model.TriggerParam;

import java.util.List;

/**
 * Created by <PERSON><PERSON>uel<PERSON> on 17/3/10.
 */
public class ExecutorRouteLast extends ExecutorRouter {

    @Override
    public ReturnT<String> route(TriggerParam triggerParam, List<String> addressList) {
        return new ReturnT<String>(addressList.get(addressList.size()-1));
    }

}
