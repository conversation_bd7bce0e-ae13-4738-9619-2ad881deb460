package com.sevb.datax.admin.exception;

import com.sevb.common.util.RequestReturnVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * Created by jwk on 2019/07/05.
 * 全局异常处理
 * <AUTHOR>
 * @date 2019/07/05 11:57
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(Exception.class)
    public RequestReturnVO handleException(Exception e){
        log.error("系统异常{0}",e);
        return RequestReturnVO.fail(e.getMessage());
    }
}
