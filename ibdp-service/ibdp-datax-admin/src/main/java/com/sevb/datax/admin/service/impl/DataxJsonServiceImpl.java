package com.sevb.datax.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.sevb.common.entity.enums.DbTypeEnum;
import com.sevb.common.util.BeanUtils;
import com.sevb.common.util.JasyptUtil;
import com.sevb.dataservice.dao.IDbConfigDao;
import com.sevb.dataservice.entity.vo.DbConfigInputVO;
import com.sevb.dataservice.entity.vo.DbConfigVO;
import com.sevb.datax.admin.dto.DataXJsonBuildDto;
import com.sevb.datax.admin.dto.DataXJsonBuildDtoCp;
import com.sevb.datax.admin.entity.JobDatasource;
import com.sevb.datax.admin.service.DataxJsonService;
import com.sevb.datax.admin.service.JobDatasourceService;
import com.sevb.datax.admin.tool.datax.DataxJsonHelper;
import com.sevb.datax.admin.tool.query.BaseQueryTool;
import com.sevb.datax.admin.tool.query.HBaseQueryTool;
import com.sevb.datax.admin.tool.query.MongoDBQueryTool;
import com.sevb.datax.admin.tool.query.QueryToolFactory;
import com.sevb.datax.admin.util.AESUtil;
import com.sevb.datax.admin.util.JdbcConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;

/**
 * com.wugui.datax json构建实现类
 *
 * <AUTHOR>
 * @ClassName DataxJsonServiceImpl
 * @Version 2.0
 * @since 2020/01/11 17:15
 */
@Service
public class DataxJsonServiceImpl implements DataxJsonService {

    @Autowired
    private JobDatasourceService jobJdbcDatasourceService;

    @Autowired
    private IDbConfigDao dbConfigDao;

    @Value("${jasypt.encryptor.password}")
    private String jasyptPassword;

    @Override
    public String buildJobJson(DataXJsonBuildDto dataXJsonBuildDto) {
        DataxJsonHelper dataxJsonHelper = new DataxJsonHelper();
        // reader
        JobDatasource readerDatasource = jobJdbcDatasourceService.getById(dataXJsonBuildDto.getReaderDatasourceId());
        // reader plugin init
        dataxJsonHelper.initReader(dataXJsonBuildDto, readerDatasource);
        JobDatasource writerDatasource = jobJdbcDatasourceService.getById(dataXJsonBuildDto.getWriterDatasourceId());
        dataxJsonHelper.initWriter(dataXJsonBuildDto, writerDatasource);

        return JSON.toJSONString(dataxJsonHelper.buildJob());
    }

    @Override
    public String buildDataxJobJson(DataXJsonBuildDtoCp dataxJsonBuildDtoCp) {
        DataxJsonHelper dataxJsonHelper = new DataxJsonHelper();
        DbConfigInputVO dbConfigVO = new DbConfigInputVO();

        dbConfigVO.setBusinessId(String.valueOf(dataxJsonBuildDtoCp.getReaderDatasourceId()));
        List<DbConfigVO> readerDbs = dbConfigDao.selectDbConfigData(dbConfigVO);
        DbConfigVO readerDb = readerDbs.get(0);
        JobDatasource readerDatasource = new JobDatasource();
        readerDatasource.setJdbcUrl(readerDb.getConfigUrl());
        readerDatasource.setJdbcUsername(AESUtil.encrypt(readerDb.getConfigUserName()));
        String readerPwd = JasyptUtil.decyptPwd(jasyptPassword,readerDb.getConfigPassword());
        readerDatasource.setJdbcPassword(AESUtil.encrypt(readerPwd));
        readerDatasource.setDatasource(readerDb.getConfigType().toLowerCase());
        readerDatasource.setJdbcDriverClass(DbTypeEnum.findEnumByType(readerDb.getConfigType()).getConnectDriver());
        List<String> columnsReaders = getColumns(readerDatasource, dataxJsonBuildDtoCp.getReaderTables().get(0));
        dataxJsonBuildDtoCp.setReaderColumns(columnsReaders);

        dbConfigVO.setBusinessId(String.valueOf(dataxJsonBuildDtoCp.getWriterDatasourceId()));
        List<DbConfigVO> writerDbs = dbConfigDao.selectDbConfigData(dbConfigVO);
        DbConfigVO writerDb = writerDbs.get(0);
        JobDatasource writerDatasource = new JobDatasource();
        writerDatasource.setJdbcUrl(writerDb.getConfigUrl());
        writerDatasource.setJdbcUsername(AESUtil.encrypt(writerDb.getConfigUserName()));
        String writerPwd = JasyptUtil.decyptPwd(jasyptPassword,writerDb.getConfigPassword());
        writerDatasource.setJdbcPassword(AESUtil.encrypt(writerPwd));
        writerDatasource.setDatasource(writerDb.getConfigType().toLowerCase());
        writerDatasource.setJdbcDriverClass(DbTypeEnum.findEnumByType(writerDb.getConfigType()).getConnectDriver());
        List<String> columnsWriters = getColumns(writerDatasource, dataxJsonBuildDtoCp.getWriterTables().get(0));
        dataxJsonBuildDtoCp.setWriterColumns(columnsWriters);
        DataXJsonBuildDto dataxJsonBuildDto = new DataXJsonBuildDto();
        BeanUtils.copyProperties(dataxJsonBuildDtoCp, dataxJsonBuildDto);
        //此处因为出现过加密后的信息无法在datax执行解析对应，所以重新赋值为明文账号密码
        readerDatasource.setJdbcUsername(readerDb.getConfigUserName());
        readerDatasource.setJdbcPassword(readerPwd);
        writerDatasource.setJdbcUsername(writerDb.getConfigUserName());
        writerDatasource.setJdbcPassword(writerPwd);

        dataxJsonHelper.initReader(dataxJsonBuildDto, readerDatasource);
        dataxJsonHelper.initWriter(dataxJsonBuildDto, writerDatasource);
        return JSON.toJSONString(dataxJsonHelper.buildJob());
    }


    public List<String> getColumns(JobDatasource datasource, String tableName) {
        try {
            if (JdbcConstants.HBASE.equals(datasource.getDatasource())) {
                return new HBaseQueryTool(datasource).getColumns(tableName);
            } else if (JdbcConstants.MONGODB.equals(datasource.getDatasource())) {
                return new MongoDBQueryTool(datasource).getColumns(tableName);
            } else {
                BaseQueryTool queryTool = QueryToolFactory.getByDbType(datasource);
                return queryTool.getColumnNames(tableName, datasource.getDatasource());
            }
        } catch (IOException e) {
            throw new RuntimeException("获取字段异常");
        }
    }
}