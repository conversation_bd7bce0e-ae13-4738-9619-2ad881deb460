package com.sevb.datax.admin.controller.old;

import com.sevb.common.util.RequestReturnVO;
import com.sevb.datax.admin.core.util.I18nUtil;
import com.sevb.datax.admin.dto.DataXJsonBuildDto;
import com.sevb.datax.admin.service.DataxJsonService;
import com.wugui.datatx.core.biz.model.ReturnT;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.sevb.common.util.RequestReturnVO.fail;
import static com.sevb.common.util.RequestReturnVO.success;

/**
 * Created by jingwk on 2020/05/05
 */

@RestController
@RequestMapping("api/dataxJson")
@Api(tags = "组装datax  json的控制器")
public class DataxJsonController {

    @Autowired
    private DataxJsonService dataxJsonService;


    @PostMapping("/buildJson")
    @ApiOperation("JSON构建")
    public ReturnT<String> buildJobJson(@RequestBody DataXJsonBuildDto dto) {
        String key = "system_please_choose";
        if (dto.getReaderDatasourceId() == null) {
            return new ReturnT<>(I18nUtil.getString(key) + I18nUtil.getString("jobinfo_field_readerDataSource"));
        }
        if (dto.getWriterDatasourceId() == null) {
            return new ReturnT<>(I18nUtil.getString(key) + I18nUtil.getString("jobinfo_field_writerDataSource"));
        }
        if (CollectionUtils.isEmpty(dto.getReaderColumns())) {
            return new ReturnT<>(I18nUtil.getString(key) + I18nUtil.getString("jobinfo_field_readerColumns"));
        }
        if (CollectionUtils.isEmpty(dto.getWriterColumns())) {
            return new ReturnT<>(I18nUtil.getString(key) + I18nUtil.getString("jobinfo_field_writerColumns"));
        }
        return new ReturnT<>(dataxJsonService.buildJobJson(dto));
    }

}
