package com.sevb.datax.admin.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * xxl-job info
 *
 * <AUTHOR>  2019-11-17 14:25:49
 */
@Data
public class JobInfo {

	@ApiModelProperty("主键ID")
	private int id;

	@ApiModelProperty("执行器主键ID")
	private int jobGroup;

	@ApiModelProperty("任务执行CRON表达式（执行计划）")
	private String jobCron;

	@ApiModelProperty("排序")
	private String jobDesc;

	@ApiModelProperty("创建时间")
	private Date addTime;

	@ApiModelProperty("更新时间")
	private Date updateTime;

	@ApiModelProperty("修改用户")
	private int userId;

	@ApiModelProperty("报警邮件")
	private String alarmEmail;

	@ApiModelProperty("执行器路由策略")
	private String executorRouteStrategy;

	@ApiModelProperty("执行器，任务Handler名称")
	private String executorHandler;

	@ApiModelProperty("执行器，任务参数")
	private String executorParam;

	@ApiModelProperty("阻塞处理策略")
	private String executorBlockStrategy;

	@ApiModelProperty("任务执行超时时间，单位秒")
	private int executorTimeout;

	@ApiModelProperty("失败重试次数")
	private int executorFailRetryCount;

	@ApiModelProperty("GLUE类型\t#com.wugui.datatx.core.glue.GlueTypeEnum")
	private String glueType;

	@ApiModelProperty("GLUE源代码")
	private String glueSource;

	@ApiModelProperty("GLUE备注")
	private String glueRemark;

	@ApiModelProperty("GLUE更新时间")
	private Date glueUpdatetime;

	@ApiModelProperty("子任务ID")
	private String childJobId;

	@ApiModelProperty("调度状态：0-停止，1-运行")
	private int triggerStatus;

	@ApiModelProperty("上次调度时间")
	private long triggerLastTime;

	@ApiModelProperty("下次调度时间")
	private long triggerNextTime;

	@ApiModelProperty("datax运行json")
	private String jobJson;

	@ApiModelProperty("脚本动态参数")
	private String replaceParam;

	@ApiModelProperty("增量日期格式")
	private String replaceParamType;

	@ApiModelProperty("jvm参数")
	private String jvmParam;

	@ApiModelProperty("增量初始时间")
	private Date incStartTime;

	@ApiModelProperty("分区信息")
	private String partitionInfo;

	@ApiModelProperty("最近一次执行状态")
	private int lastHandleCode;

	@ApiModelProperty("所属项目Id")
	private int projectId;

	@ApiModelProperty("主键字段")
	private String primaryKey;

	@ApiModelProperty("增量初始id")
	private Long incStartId;

	@ApiModelProperty("增量方式")
	private int incrementType;

	@ApiModelProperty("datax的读表")
	private  String readerTable;

	@ApiModelProperty("数据源id")
	private int datasourceId;

	@TableField(exist=false)
	private String projectName;

	@TableField(exist=false)

	@ApiModelProperty("任务名称")
	private String jobName;
	@ApiModelProperty("采集后归属目录")
	private String belongingDirectory;
	@ApiModelProperty("负责人")
	private String pic;
	@ApiModelProperty("采集失败告警")
	private String failedAlarm;

	@ApiModelProperty("属性（数据采集的表）")
	private String tables;
	@ApiModelProperty("规则配置类别（周期取数，手动采集）")
	private String category;
	@ApiModelProperty("任务开始时间")
	private Date startTime;
	@ApiModelProperty("数据范围（手动采集）")
	private String valueRange;
	@ApiModelProperty("数据范围-开始时间")
	private Date dataRangeStart;
	@ApiModelProperty("数据范围-截止时间")
	private Date dataRangeEnd;


	@ApiModelProperty("任务状态")
	private int taskStatus;

	@ApiModelProperty("采集数据源id")
	private String cdsId;
	@ApiModelProperty("目标数据源id")
	private String tdsId;
	@ApiModelProperty("创建人")
	private String createBy;
	@ApiModelProperty("更新人")
	private String updateBy;

	@ApiModelProperty("数据来源")
	private String dsType;

	@ApiModelProperty("节点id")
	private Integer nodeId;

	@ApiModelProperty("采集数据源名称")
	private String cdsName;
}
