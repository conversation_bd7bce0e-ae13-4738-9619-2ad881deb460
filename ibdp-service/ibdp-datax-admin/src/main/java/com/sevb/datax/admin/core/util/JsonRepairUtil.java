package com.sevb.datax.admin.core.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * JSON修复工具类
 * 用于修复包含控制字符的JSON字符串
 * 
 * <AUTHOR>
 */
public class JsonRepairUtil {
    private static final Logger logger = LoggerFactory.getLogger(JsonRepairUtil.class);
    
    // 独立的ObjectMapper实例，避免循环调用JacksonUtil
    private static final ObjectMapper REPAIR_MAPPER = new ObjectMapper();

    /**
     * 修复JSON字符串中的控制字符
     * 
     * @param jsonString 原始JSON字符串
     * @return 修复后的JSON字符串
     */
    public static String repairJson(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return jsonString;
        }

        try {
            // 首先尝试直接解析，如果成功则返回原字符串
            REPAIR_MAPPER.readValue(jsonString, Object.class);
            return jsonString;
        } catch (Exception e) {
            logger.warn("JSON parsing failed, attempting to repair: {}", e.getMessage());

            // 检查是否包含控制字符
            if (hasControlChars(jsonString)) {
                logger.info("JSON contains control characters: {}", getControlCharInfo(jsonString));

                // 先尝试简单清理
                String simpleCleaned = cleanControlChars(jsonString);
                try {
                    REPAIR_MAPPER.readValue(simpleCleaned, Object.class);
                    logger.info("Simple cleaning successful");
                    return simpleCleaned;
                } catch (Exception simpleException) {
                    logger.debug("Simple cleaning failed, trying advanced repair");
                }
            }

            return repairJsonInternal(jsonString);
        }
    }

    /**
     * 内部JSON修复实现
     */
    private static String repairJsonInternal(String jsonString) {
        StringBuilder repaired = new StringBuilder();
        boolean inString = false;
        boolean escaped = false;
        int i = 0;

        while (i < jsonString.length()) {
            char c = jsonString.charAt(i);

            if (escaped) {
                // 处理转义字符
                repaired.append(c);
                escaped = false;
            } else if (c == '\\') {
                // 转义字符
                repaired.append(c);
                escaped = true;
            } else if (c == '"') {
                // 字符串边界
                repaired.append(c);
                inString = !inString;
            } else if (inString) {
                // 在字符串内部
                if (isValidJsonStringChar(c)) {
                    repaired.append(c);
                } else {
                    // 替换无效字符为转义序列或删除
                    if (c < 32) {
                        switch (c) {
                            case '\b':
                                repaired.append("\\b");
                                break;
                            case '\f':
                                repaired.append("\\f");
                                break;
                            case '\n':
                                repaired.append("\\n");
                                break;
                            case '\r':
                                repaired.append("\\r");
                                break;
                            case '\t':
                                repaired.append("\\t");
                                break;
                            default:
                                // 其他控制字符用Unicode转义
                                repaired.append(String.format("\\u%04x", (int) c));
                                break;
                        }
                    } else {
                        repaired.append(c);
                    }
                }
            } else {
                // 在字符串外部
                if (isValidJsonStructureChar(c)) {
                    repaired.append(c);
                }
                // 忽略无效的结构字符
            }
            i++;
        }

        String result = repaired.toString();

        // 验证修复结果
        try {
            REPAIR_MAPPER.readValue(result, Object.class);
            logger.debug("JSON repair successful");
            return result;
        } catch (Exception e) {
            logger.warn("JSON repair failed, returning original string: {}", e.getMessage());
            return jsonString;
        }
    }

    /**
     * 检查字符是否是有效的JSON字符串字符
     */
    private static boolean isValidJsonStringChar(char c) {
        // 允许可打印字符和必要的空白字符
        return c >= 32 || c == 9 || c == 10 || c == 13 || c == 8 || c == 12;
    }

    /**
     * 检查字符是否是有效的JSON结构字符
     */
    private static boolean isValidJsonStructureChar(char c) {
        // 允许可打印字符和必要的空白字符
        return c >= 32 || c == 9 || c == 10 || c == 13;
    }

    /**
     * 检查字符串是否包含控制字符
     */
    public static boolean hasControlChars(String str) {
        if (str == null) return false;
        
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (c < 32 && c != 9 && c != 10 && c != 13) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取控制字符信息
     */
    public static String getControlCharInfo(String str) {
        if (str == null) return "null";
        
        StringBuilder info = new StringBuilder();
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (c < 32 && c != 9 && c != 10 && c != 13) {
                if (info.length() > 0) info.append(", ");
                info.append("pos=").append(i).append(",code=").append((int)c);
            }
        }
        return info.toString();
    }

    /**
     * 简单清理控制字符
     */
    public static String cleanControlChars(String str) {
        if (str == null) return null;
        
        StringBuilder cleaned = new StringBuilder();
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (c >= 32 || c == 9 || c == 10 || c == 13) {
                cleaned.append(c);
            }
            // 忽略其他控制字符
        }
        return cleaned.toString();
    }
}
