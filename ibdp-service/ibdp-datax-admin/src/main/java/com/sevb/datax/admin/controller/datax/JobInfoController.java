package com.sevb.datax.admin.controller.datax;


import com.github.pagehelper.PageInfo;
import com.sevb.common.util.RequestReturnVO;
import com.sevb.datax.admin.core.cron.CronExpression;
import com.sevb.datax.admin.core.thread.JobTriggerPoolHelper;
import com.sevb.datax.admin.core.trigger.TriggerTypeEnum;
import com.sevb.datax.admin.core.util.I18nUtil;
import com.sevb.datax.admin.dto.*;
import com.sevb.datax.admin.entity.JobInfo;
import com.sevb.datax.admin.result.ReturnTDatax;
import com.sevb.datax.admin.service.DataxJsonService;
import com.sevb.datax.admin.service.JobService;
import com.sevb.datax.admin.vo.JobInfoParams;
import com.sevb.datax.admin.vo.JobInfoVO;
import com.wugui.datatx.core.biz.model.ReturnT;
import com.wugui.datatx.core.util.DateUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.text.ParseException;
import java.util.*;
import static com.sevb.datax.admin.tool.convert.ClassConvert.*;

/**
 * index controller
 *
 * <AUTHOR> 2015-12-19 16:13:16
 */
@Api(tags = "采集任务配置接口")
@RestController
@RequestMapping("/api/job")
public class JobInfoController {

    @Resource
    private JobService jobService;

    @Autowired
    private DataxJsonService dataxJsonService;
    @PostMapping("/pageListNew")
    @ApiOperation("任务列表")
    public ReturnTDatax<PageInfo<JobInfoVO>> pageListNew(@RequestParam(required = false, defaultValue = "1") int pageNum,
                                                         @RequestParam(required = false, defaultValue = "10") int pageSize,
                                                         @RequestBody JobInfoParams jobInfoParams) {
        return new ReturnTDatax<PageInfo<JobInfoVO>>().convert(jobService.pageListNew(pageNum, pageSize, jobInfoParams));
    }

    @GetMapping("/list")
    @ApiOperation("全部任务列表")
    public ReturnTDatax<List<JobInfo>> list(){
        return new ReturnTDatax<List<JobInfo>>().convert(jobService.list());
    }

    @PostMapping("/add")
    @ApiOperation("添加任务")
    public ReturnTDatax<String> add(HttpServletRequest request, @RequestBody JobInfoVO vo) {
        //构建datax jobjson
        DataXJsonBuildDtoCp dataxJsonBuildDtoCp = builderJsonParams(vo);
        String jobJson = dataxJsonService.buildDataxJobJson(dataxJsonBuildDtoCp);
        try {
            JobInfo jobInfo = voConvertInfo(vo);
            jobInfo.setJobJson(jobJson);
            return new ReturnTDatax<String>().convert(jobService.add(jobInfo));
        } catch (Exception e) {
            return ReturnTDatax.FAIL;
        }
    }

    @PostMapping("/update")
    @ApiOperation("更新任务")
    public ReturnTDatax<String> update(HttpServletRequest request,@RequestBody JobInfoVO vo) {
        //构建datax jobjson
        DataXJsonBuildDtoCp dataxJsonBuildDtoCp = builderJsonParams(vo);
        String jobJson = dataxJsonService.buildDataxJobJson(dataxJsonBuildDtoCp);
        try {
            JobInfo jobInfo = voConvertInfo(vo);
            jobInfo.setJobJson(jobJson);
            return new ReturnTDatax<String>().convert(jobService.update(jobInfo));
        } catch (Exception e) {
            return ReturnTDatax.FAIL;
        }
    }

    @PostMapping(value = "/remove/{id}")
    @ApiOperation("移除任务")
    public ReturnTDatax<String> remove(@PathVariable(value = "id") int id) {
        return new ReturnTDatax<String>().convert(jobService.remove(id));
    }

    @RequestMapping(value = "/stop",method = RequestMethod.POST)
    @ApiOperation("停止任务-(任务下线)")
    public ReturnTDatax<String> pause(int id) {
        return new ReturnTDatax<String>().convert(jobService.stop(id));
    }

    @RequestMapping(value = "/start",method = RequestMethod.POST)
    @ApiOperation("开启任务-(任务上线)")
    public ReturnTDatax<String> start(int id) {
        return new ReturnTDatax<String>().convert(jobService.start(id));
    }

    @PostMapping(value = "/trigger")
    @ApiOperation("触发任务")
    public ReturnTDatax<String> triggerJob(@RequestBody TriggerJobDto dto) {
        String executorParam=dto.getExecutorParam();
        if (executorParam == null) {
            executorParam = "";
        }
        JobTriggerPoolHelper.trigger(dto.getJobId(), TriggerTypeEnum.MANUAL, -1, null, executorParam);
        return ReturnTDatax.SUCCESS;
    }
    //
    // @GetMapping("/nextTriggerTime")
    // @ApiOperation("获取近5次触发时间")
    // public ReturnTDatax<List<String>> nextTriggerTime(String cron) {
    //     List<String> result = new ArrayList<>();
    //     try {
    //         CronExpression cronExpression = new CronExpression(cron);
    //         Date lastTime = new Date();
    //         for (int i = 0; i < 5; i++) {
    //             lastTime = cronExpression.getNextValidTimeAfter(lastTime);
    //             if (lastTime != null) {
    //                 result.add(DateUtil.formatDateTime(lastTime));
    //             } else {
    //                 break;
    //             }
    //         }
    //     } catch (ParseException e) {
    //         return new ReturnTDatax<List<String>>(String.valueOf(ReturnT.FAIL_CODE), I18nUtil.getString("jobinfo_field_cron_invalid"));
    //     }
    //     return new ReturnTDatax<List<String>>().convert(result);
    // }
    //
    // @PostMapping("/batchAdd")
    // @ApiOperation("批量创建任务")
    // public ReturnTDatax<String> batchAdd(@RequestBody DataXBatchJsonBuildDto dto) throws IOException {
    //     if (dto.getTemplateId() ==0) {
    //         return new ReturnTDatax<String>(String.valueOf(ReturnT.FAIL_CODE), (I18nUtil.getString("system_please_choose") + I18nUtil.getString("jobinfo_field_temp")));
    //     }
    //     return new ReturnTDatax<String>().convert(jobService.batchAdd(dto));
    // }
}
