package com.sevb.datax.admin.result;

import com.wugui.datatx.core.biz.model.ReturnT;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description: TODO
 * @Author: Creators
 * @Date: 2025年04月17日 12:07:43
 */
@Getter
@Setter
public class ReturnTDatax<T> {

    public static final ReturnTDatax<String> FAIL = new ReturnTDatax<>("500", "操作失败");
    public static final ReturnTDatax<String> SUCCESS = new ReturnTDatax<>("200", "操作成功");
    private String status;

    private String code;

    private String message;

    private String url;

    private T resultObjVO;

    public ReturnTDatax() {
    }

    public ReturnTDatax(String status, String message) {
        this.status = status;
        this.message = message;
    }

    public ReturnTDatax(String status, String message, T resultObjVO) {
        this.status = status;
        this.message = message;
        this.resultObjVO = resultObjVO;
    }

    public ReturnTDatax<T> convert(ReturnT<T> returnT) {
        ReturnTDatax<T> vo = new ReturnTDatax<T>();
        if (returnT.getCode() == ReturnT.SUCCESS_CODE) {
            vo.setResultObjVO((T) returnT.getContent());
            vo.setStatus(String.valueOf(ReturnT.SUCCESS_CODE));
            vo.setMessage("操作成功");
            return vo;
        } else {
            vo.setResultObjVO((T) returnT.getContent());
            vo.setStatus(String.valueOf(ReturnT.FAIL_CODE));
            vo.setMessage(returnT.getMsg());
            return vo;
        }
    }

    public ReturnTDatax<T> convert(T content) {
        ReturnTDatax<T> vo = new ReturnTDatax<>();
        vo.setResultObjVO(content);
        vo.setStatus(String.valueOf(ReturnT.SUCCESS_CODE));
        vo.setMessage("操作成功");
        return vo;
    }
}
