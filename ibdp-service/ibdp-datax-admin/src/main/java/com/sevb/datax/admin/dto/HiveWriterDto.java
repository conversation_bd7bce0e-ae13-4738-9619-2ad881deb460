package com.sevb.datax.admin.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 构建hive write dto
 *
 * <AUTHOR>
 * @ClassName hive write dto
 * @Version 2.0
 * @since 2020/01/11 17:15
 */
@Data
public class HiveWriterDto implements Serializable {

    private String writerDefaultFS;

    private String writerFileType;

    private String writerPath;

    private String writerFileName;

    private String writeMode;

    private String writeFieldDelimiter;
}
