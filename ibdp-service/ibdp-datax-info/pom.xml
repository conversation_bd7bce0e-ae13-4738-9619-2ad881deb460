<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.sevb</groupId>
        <artifactId>ibdp-service</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <packaging>pom</packaging>

    <modules>
        <module>ibdp-datax-core</module>
        <module>ibdp-datax-rpc</module>
    </modules>
    <artifactId>ibdp-datax-info</artifactId>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.test.skip>true</maven.test.skip>
        <fastjson.version>1.2.83</fastjson.version>
        <slf4j-api.version>1.7.28</slf4j-api.version>
        <logback-classic.version>1.2.2</logback-classic.version>
        <postgresql.version>42.2.5</postgresql.version>
        <mysql-connector.version>8.0.17</mysql-connector.version>
        <jna.version>4.5.1</jna.version>
        <mybatisplus.version>3.4.3</mybatisplus.version>
        <spring.version>4.3.25.RELEASE</spring.version>
        <hadoop.version>2.7.3</hadoop.version>
        <hive.jdbc.version>2.1.0</hive.jdbc.version>
        <hbase.version>1.3.0</hbase.version>
        <mongo-java-driver.version>3.4.2</mongo-java-driver.version>
        <phoenix.version>5.0.0-HBase-2.0</phoenix.version>
        <hutool.version>5.8.22</hutool.version>
        <oshi.core.version>3.5.0</oshi.core.version>
        <hessian.version>4.0.63</hessian.version>
    </properties>


</project>