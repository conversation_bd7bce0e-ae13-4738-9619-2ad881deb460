package com.wugui.datatx.core.util;

public final class Constants {

    public static final String MYSQL_DATABASE = "Unknown database";
    public static final String MYSQL_CONNEXP = "Communications link failure";
    public static final String MYSQL_ACCDENIED = "Access denied";
    public static final String MYSQL_TABLE_NAME_ERR1 = "Table";
    public static final String MYSQL_TABLE_NAME_ERR2 = "doesn't exist";
    public static final String MYSQL_SELECT_PRI = "SELECT command denied to user";
    public static final String MYSQL_COLUMN1 = "Unknown column";
    public static final String MYSQL_COLUMN2 = "field list";
    public static final String MYSQL_WHERE = "where clause";

    public static final String ORACLE_DATABASE = "ORA-12505";
    public static final String ORACLE_CONNEXP = "The Network Adapter could not establish the connection";
    public static final String ORACLE_ACCDENIED = "ORA-01017";
    public static final String ORACLE_TABLE_NAME = "table or view does not exist";
    public static final String ORACLE_SELECT_PRI = "insufficient privileges";
    public static final String ORACLE_SQL = "invalid identifier";


    public static final String SPLIT_COMMA = ",";
    public static final String SPLIT_AT = "@";
    public static final String SPLIT_COLON = ";";
    public static final String SPLIT_POINT = ".";
    public static final String SPLIT_SCOLON=":";
    public static final String SPLIT_HYPHEN = "-";
    public static final String SPLIT_DIVIDE = "/";
    public static final String SPLIT_STAR = "*";
    public static final String SPLIT_QUESTION = "?";
    public static final String EQUAL = "=";
    public static final String SPLIT_AMPERSAND = "&";
    public static final String AND = "AND";
    public static final String SPACE = " ";
    public static final String STRING_BLANK = "";
    public static final String MONGO_URL_PREFIX = "mongodb://";

}
