
spring:
  #数据源
#  datasource:
#    username: root
#    password: root
#    url: **********************************************************************************************************************************************************************************
#    driver-class-name: com.mysql.jdbc.Driver
#    hikari:
#      ## 最小空闲连接数量
#      minimum-idle: 5
#      ## 空闲连接存活最大时间，默认600000（10分钟）
#      idle-timeout: 180000
#      ## 连接池最大连接数，默认是10
#      maximum-pool-size: 10
#      ## 数据库连接超时时间,默认30秒，即30000
#      connection-timeout: 30000
#      connection-test-query: SELECT 1
#      ##此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
#      max-lifetime: 1800000

  # datax-web email
  mail:
    host: smtp.qq.com
    port: 25
    username: <EMAIL>
    password: xxx
    #    username: ${mail.username}
    #    password: ${mail.password}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
        socketFactory:
          class: javax.net.ssl.SSLSocketFactory


management:
  health:
    mail:
      enabled: false
  server:
    servlet:
      context-path: /actuator

mybatis-plus:
  # mapper.xml文件扫描
  mapper-locations: classpath*:/mybatis-mapper/*Mapper.xml
  # 实体扫描，多个package用逗号或者分号分隔
  #typeAliasesPackage: com.yibo.essyncclient.*.entity
  global-config:
    # 数据库相关配置
    db-config:
      # 主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: AUTO
      # 字段策略 IGNORED:"忽略判断",NOT_NULL:"非 NULL 判断"),NOT_EMPTY:"非空判断"
      field-strategy: NOT_NULL
      # 驼峰下划线转换
      column-underline: true
      # 逻辑删除
      logic-delete-value: 0
      logic-not-delete-value: 1
      # 数据库类型
      db-type: postgresql
    banner: false
  # mybatis原生配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    type-handlers-package: com.sevb.datax.admin.core.handler

# 配置mybatis-plus打印sql日志
logging:
  level:
    com.sevb.datax.admin.mapper: debug
    # 添加更多包的SQL日志配置
    com.sevb.development.dao: debug
    com.sevb.development.mapper: debug
    com.sevb.dataservice.dao: debug
    com.sevb.project.dao: debug
    com.sevb.system.dao: debug
    # MyBatis框架日志
    com.apache.ibatis: debug
    java.sql.Connection: debug
    java.sql.Statement: debug
    java.sql.PreparedStatement: debug
path: ./data/applogs/admin
#  level:
#    com.sevb.datax.admin.mapper: error
#    path: ${data.path}/applogs/admin



#datax-job, access token
datax:
  job:
    accessToken:
    #i18n (default empty as chinese, "en" as english)
    i18n:
    # triggerpool max size
    triggerpool:
      fast:
        max: 200
      slow:
        max: 100
      ### log retention days
    logretentiondays: 30

datasource:
  aes:
    key: AD42F6697B035B75